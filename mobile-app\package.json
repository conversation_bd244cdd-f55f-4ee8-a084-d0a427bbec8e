{"name": "nandinihub-mobile", "version": "1.0.0", "description": "Nandini Hub Mobile App - Premium Puja Samagri Shopping", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all"}, "dependencies": {"@expo/config-plugins": "^10.1.2", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/cli-server-api": "^18.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.0", "expo": "~53.0.0", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-device": "~7.1.4", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "~18.1.6", "expo-notifications": "^0.31.4", "expo-screen-orientation": "~8.1.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "^0.30.10", "metro": "^0.82.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.47.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.1", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@types/react": "~19.0.10", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "^5.1.3"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile-app", "ecommerce", "puja-samagri", "shopping"], "author": "Nandini Hub Team", "license": "MIT"}