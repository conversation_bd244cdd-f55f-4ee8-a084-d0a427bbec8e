{"name": "nandinihub-mobile", "version": "1.0.0", "description": "Nandini Hub Mobile App - Premium Puja Samagri Shopping", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all"}, "dependencies": {"expo": "~49.0.15", "react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "@expo/vector-icons": "^13.0.0", "react-native-paper": "^5.11.1", "@react-native-async-storage/async-storage": "1.18.2", "expo-secure-store": "~12.3.1", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "expo-image": "~1.3.2", "expo-linear-gradient": "~12.3.0", "expo-blur": "~12.4.1", "expo-haptics": "~12.4.0", "expo-sharing": "~11.5.0", "expo-device": "~5.4.0", "expo-splash-screen": "~0.20.5", "expo-screen-orientation": "~6.0.5", "expo-image-picker": "~14.3.2", "expo-camera": "~13.4.4", "expo-location": "~16.1.0", "expo-notifications": "~0.20.1", "react-native-ratings": "^8.1.0", "react-native-modal": "^13.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@expo/webpack-config": "^19.0.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "^5.1.3"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile-app", "ecommerce", "puja-samagri", "shopping"], "author": "Nandini Hub Team", "license": "MIT"}