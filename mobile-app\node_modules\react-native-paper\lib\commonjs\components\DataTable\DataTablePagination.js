"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DataTablePagination = void 0;
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _color = _interopRequireDefault(require("color"));
var _theming = require("../../core/theming");
var _Button = _interopRequireDefault(require("../Button/Button"));
var _IconButton = _interopRequireDefault(require("../IconButton/IconButton"));
var _MaterialCommunityIcon = _interopRequireDefault(require("../MaterialCommunityIcon"));
var _Menu = _interopRequireDefault(require("../Menu/Menu"));
var _Text = _interopRequireDefault(require("../Typography/Text"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }
const PaginationControls = ({
  page,
  numberOfPages,
  onPageChange,
  showFastPaginationControls,
  theme: themeOverrides,
  paginationControlRippleColor
}) => {
  const theme = (0, _theming.useInternalTheme)(themeOverrides);
  const textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;
  return /*#__PURE__*/React.createElement(React.Fragment, null, showFastPaginationControls ? /*#__PURE__*/React.createElement(_IconButton.default, {
    icon: ({
      size,
      color
    }) => /*#__PURE__*/React.createElement(_MaterialCommunityIcon.default, {
      name: "page-first",
      color: color,
      size: size,
      direction: _reactNative.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'
    }),
    iconColor: textColor,
    rippleColor: paginationControlRippleColor,
    disabled: page === 0,
    onPress: () => onPageChange(0),
    accessibilityLabel: "page-first",
    theme: theme
  }) : null, /*#__PURE__*/React.createElement(_IconButton.default, {
    icon: ({
      size,
      color
    }) => /*#__PURE__*/React.createElement(_MaterialCommunityIcon.default, {
      name: "chevron-left",
      color: color,
      size: size,
      direction: _reactNative.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'
    }),
    iconColor: textColor,
    rippleColor: paginationControlRippleColor,
    disabled: page === 0,
    onPress: () => onPageChange(page - 1),
    accessibilityLabel: "chevron-left",
    theme: theme
  }), /*#__PURE__*/React.createElement(_IconButton.default, {
    icon: ({
      size,
      color
    }) => /*#__PURE__*/React.createElement(_MaterialCommunityIcon.default, {
      name: "chevron-right",
      color: color,
      size: size,
      direction: _reactNative.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'
    }),
    iconColor: textColor,
    rippleColor: paginationControlRippleColor,
    disabled: numberOfPages === 0 || page === numberOfPages - 1,
    onPress: () => onPageChange(page + 1),
    accessibilityLabel: "chevron-right",
    theme: theme
  }), showFastPaginationControls ? /*#__PURE__*/React.createElement(_IconButton.default, {
    icon: ({
      size,
      color
    }) => /*#__PURE__*/React.createElement(_MaterialCommunityIcon.default, {
      name: "page-last",
      color: color,
      size: size,
      direction: _reactNative.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'
    }),
    iconColor: textColor,
    rippleColor: paginationControlRippleColor,
    disabled: numberOfPages === 0 || page === numberOfPages - 1,
    onPress: () => onPageChange(numberOfPages - 1),
    accessibilityLabel: "page-last",
    theme: theme
  }) : null);
};
const PaginationDropdown = ({
  numberOfItemsPerPageList,
  numberOfItemsPerPage,
  onItemsPerPageChange,
  theme: themeOverrides,
  selectPageDropdownRippleColor,
  dropdownItemRippleColor
}) => {
  const theme = (0, _theming.useInternalTheme)(themeOverrides);
  const {
    colors
  } = theme;
  const [showSelect, toggleSelect] = React.useState(false);
  return /*#__PURE__*/React.createElement(_Menu.default, {
    visible: showSelect,
    onDismiss: () => toggleSelect(!showSelect),
    theme: theme,
    anchor: /*#__PURE__*/React.createElement(_Button.default, {
      mode: "outlined",
      onPress: () => toggleSelect(true),
      style: styles.button,
      icon: "menu-down",
      contentStyle: styles.contentStyle,
      theme: theme,
      rippleColor: selectPageDropdownRippleColor
    }, `${numberOfItemsPerPage}`)
  }, numberOfItemsPerPageList === null || numberOfItemsPerPageList === void 0 ? void 0 : numberOfItemsPerPageList.map(option => /*#__PURE__*/React.createElement(_Menu.default.Item, {
    key: option,
    titleStyle: option === numberOfItemsPerPage && {
      color: colors === null || colors === void 0 ? void 0 : colors.primary
    },
    onPress: () => {
      onItemsPerPageChange === null || onItemsPerPageChange === void 0 || onItemsPerPageChange(option);
      toggleSelect(false);
    },
    rippleColor: dropdownItemRippleColor,
    title: option,
    theme: theme
  })));
};

/**
 * A component to show pagination for data table.
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { DataTable } from 'react-native-paper';
 *
 * const numberOfItemsPerPageList = [2, 3, 4];
 *
 * const items = [
 *   {
 *     key: 1,
 *     name: 'Page 1',
 *   },
 *   {
 *     key: 2,
 *     name: 'Page 2',
 *   },
 *   {
 *     key: 3,
 *     name: 'Page 3',
 *   },
 * ];
 *
 * const MyComponent = () => {
 *   const [page, setPage] = React.useState(0);
 *   const [numberOfItemsPerPage, onItemsPerPageChange] = React.useState(numberOfItemsPerPageList[0]);
 *   const from = page * numberOfItemsPerPage;
 *   const to = Math.min((page + 1) * numberOfItemsPerPage, items.length);
 *
 *   React.useEffect(() => {
 *      setPage(0);
 *   }, [numberOfItemsPerPage]);
 *
 *   return (
 *     <DataTable>
 *       <DataTable.Pagination
 *         page={page}
 *         numberOfPages={Math.ceil(items.length / numberOfItemsPerPage)}
 *         onPageChange={page => setPage(page)}
 *         label={`${from + 1}-${to} of ${items.length}`}
 *         showFastPaginationControls
 *         numberOfItemsPerPageList={numberOfItemsPerPageList}
 *         numberOfItemsPerPage={numberOfItemsPerPage}
 *         onItemsPerPageChange={onItemsPerPageChange}
 *         selectPageDropdownLabel={'Rows per page'}
 *       />
 *     </DataTable>
 *   );
 * };
 *
 * export default MyComponent;
 * ```
 */
const DataTablePagination = ({
  label,
  accessibilityLabel,
  page,
  numberOfPages,
  onPageChange,
  style,
  showFastPaginationControls = false,
  numberOfItemsPerPageList,
  numberOfItemsPerPage,
  onItemsPerPageChange,
  selectPageDropdownLabel,
  selectPageDropdownAccessibilityLabel,
  selectPageDropdownRippleColor,
  dropdownItemRippleColor,
  theme: themeOverrides,
  ...rest
}) => {
  const theme = (0, _theming.useInternalTheme)(themeOverrides);
  const labelColor = (0, _color.default)(theme.isV3 ? theme.colors.onSurface : theme === null || theme === void 0 ? void 0 : theme.colors.text).alpha(0.6).rgb().string();
  return /*#__PURE__*/React.createElement(_reactNative.View, _extends({}, rest, {
    style: [styles.container, style],
    accessibilityLabel: "pagination-container"
  }), numberOfItemsPerPageList && numberOfItemsPerPage && onItemsPerPageChange && /*#__PURE__*/React.createElement(_reactNative.View, {
    accessibilityLabel: "Options Select",
    style: styles.optionsContainer
  }, /*#__PURE__*/React.createElement(_Text.default, {
    style: [styles.label, {
      color: labelColor
    }],
    numberOfLines: 3,
    accessibilityLabel: selectPageDropdownAccessibilityLabel || 'selectPageDropdownLabel'
  }, selectPageDropdownLabel), /*#__PURE__*/React.createElement(PaginationDropdown, {
    numberOfItemsPerPageList: numberOfItemsPerPageList,
    numberOfItemsPerPage: numberOfItemsPerPage,
    onItemsPerPageChange: onItemsPerPageChange,
    selectPageDropdownRippleColor: selectPageDropdownRippleColor,
    dropdownItemRippleColor: dropdownItemRippleColor,
    theme: theme
  })), /*#__PURE__*/React.createElement(_Text.default, {
    style: [styles.label, {
      color: labelColor
    }],
    numberOfLines: 3,
    accessibilityLabel: accessibilityLabel || 'label'
  }, label), /*#__PURE__*/React.createElement(_reactNative.View, {
    style: styles.iconsContainer
  }, /*#__PURE__*/React.createElement(PaginationControls, {
    showFastPaginationControls: showFastPaginationControls,
    onPageChange: onPageChange,
    page: page,
    numberOfPages: numberOfPages,
    theme: theme
  })));
};
exports.DataTablePagination = DataTablePagination;
DataTablePagination.displayName = 'DataTable.Pagination';
const styles = _reactNative.StyleSheet.create({
  container: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    flexWrap: 'wrap'
  },
  optionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 6
  },
  label: {
    fontSize: 12,
    marginRight: 16
  },
  button: {
    textAlign: 'center',
    marginRight: 16
  },
  iconsContainer: {
    flexDirection: 'row'
  },
  contentStyle: {
    flexDirection: 'row-reverse'
  }
});
var _default = exports.default = DataTablePagination; // @component-docs ignore-next-line
//# sourceMappingURL=DataTablePagination.js.map