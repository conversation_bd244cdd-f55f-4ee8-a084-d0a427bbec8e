{"version": 3, "names": ["ComplexAnimationBuilder", "LinearTransition", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "callback", "callbackV", "delay", "get<PERSON>elay", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "Layout"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultTransitions/LinearTransition.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,uBAAuB,QAAQ,8BAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,kBAAkB;EAEtC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,gBAAgB,CAAC,CAAC;EAC/B;EAEAG,KAAK,GAAGA,CAAA,KAA+B;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAE7B,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,aAAa,EAAE;UACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;UAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;UAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;UAC1BC,MAAM,EAAER,MAAM,CAACS;QACjB,CAAC;QACDC,UAAU,EAAE;UACVR,OAAO,EAAEX,aAAa,CACpBO,KAAK,EACLL,SAAS,CAACO,MAAM,CAACW,aAAa,EAAEjB,MAAM,CACxC,CAAC;UACDU,OAAO,EAAEb,aAAa,CACpBO,KAAK,EACLL,SAAS,CAACO,MAAM,CAACY,aAAa,EAAElB,MAAM,CACxC,CAAC;UACDY,KAAK,EAAEf,aAAa,CAACO,KAAK,EAAEL,SAAS,CAACO,MAAM,CAACa,WAAW,EAAEnB,MAAM,CAAC,CAAC;UAClEc,MAAM,EAAEjB,aAAa,CAACO,KAAK,EAAEL,SAAS,CAACO,MAAM,CAACc,YAAY,EAAEpB,MAAM,CAAC;QACrE,CAAC;QACDE;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA,OAAO,MAAMmB,MAAM,GAAG5B,gBAAgB", "ignoreList": []}