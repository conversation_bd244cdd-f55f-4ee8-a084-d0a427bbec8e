import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { theme } from '../../styles/theme';

const CheckoutScreen = ({ navigation }) => {
  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Checkout</Text>
          <Text style={styles.message}>
            This screen will handle the checkout process including:
            {'\n\n'}• Address selection
            {'\n'}• Payment method selection
            {'\n'}• Order summary
            {'\n'}• Order placement
            {'\n'}• Payment processing
          </Text>
          <Button
            mode="contained"
            onPress={() => {
              // Simulate order placement
              navigation.navigate('Profile', { screen: 'Orders' });
            }}
            style={styles.button}
          >
            Place Order (Demo)
          </Button>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  card: {
    elevation: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: theme.spacing.lg,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    color: theme.colors.text,
    marginBottom: theme.spacing.xl,
  },
  button: {
    backgroundColor: theme.colors.primary,
  },
});

export default CheckoutScreen;
