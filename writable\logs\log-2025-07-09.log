CRITICAL - 2025-07-09 04:44:01 --> ErrorException: Undefined array key "parent_id"
[Method: GET, Route: api/v1/categories/tree]
in APPPATH\Controllers\Api\CategoryApiController.php on line 99.
 1 APPPATH\Controllers\Api\CategoryApiController.php(99): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "parent_id"', 'D:\\xampp\\htdocs\\nandinihub\\app\\Controllers\\Api\\CategoryApiController.php', 99)
 2 APPPATH\Controllers\Api\CategoryApiController.php(86): App\Controllers\Api\CategoryApiController->buildCategoryTree([...])
 3 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Api\CategoryApiController->tree()
 4 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api\CategoryApiController))
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-07-09 05:06:42 --> ErrorException: Undefined array key "parent_id"
[Method: GET, Route: api/v1/categories/tree]
in APPPATH\Controllers\Api\CategoryApiController.php on line 99.
 1 APPPATH\Controllers\Api\CategoryApiController.php(99): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "parent_id"', 'D:\\xampp\\htdocs\\nandinihub\\app\\Controllers\\Api\\CategoryApiController.php', 99)
 2 APPPATH\Controllers\Api\CategoryApiController.php(86): App\Controllers\Api\CategoryApiController->buildCategoryTree([...])
 3 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Api\CategoryApiController->tree()
 4 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api\CategoryApiController))
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
