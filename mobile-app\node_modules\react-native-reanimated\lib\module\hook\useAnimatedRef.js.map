{"version": 3, "names": ["useRef", "Platform", "getShadowNodeWrapperFromRef", "isF<PERSON><PERSON>", "isWeb", "findNodeHandle", "shareableMappingCache", "makeShareableCloneRecursive", "useSharedValue", "IS_WEB", "getComponentOrScrollable", "component", "getNativeScrollRef", "getScrollableNode", "useAnimatedRef", "tag", "viewName", "ref", "current", "fun", "getTagValueFunction", "getTagOrShadowNodeWrapper", "value", "getTag", "OS", "viewConfig", "uiViewClassName", "animatedRefShareableHandle", "__init", "f", "set"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedRef.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,OAAO;AAE9B,SAASC,QAAQ,QAAQ,cAAc;AAGvC,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,QAAQ,EAAEC,KAAK,QAAQ,uBAAoB;AACpD,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,qBAAqB,QAAQ,6BAA0B;AAChE,SAASC,2BAA2B,QAAQ,kBAAe;AAE3D,SAASC,cAAc,QAAQ,qBAAkB;AAEjD,MAAMC,MAAM,GAAGL,KAAK,CAAC,CAAC;AAYtB,SAASM,wBAAwBA,CAACC,SAAmC,EAAE;EACrE,IAAIR,QAAQ,CAAC,CAAC,IAAIQ,SAAS,CAACC,kBAAkB,EAAE;IAC9C,OAAOD,SAAS,CAACC,kBAAkB,CAAC,CAAC;EACvC,CAAC,MAAM,IAAI,CAACT,QAAQ,CAAC,CAAC,IAAIQ,SAAS,CAACE,iBAAiB,EAAE;IACrD,OAAOF,SAAS,CAACE,iBAAiB,CAAC,CAAC;EACtC;EACA,OAAOF,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAAA,EAED;EAC3B,MAAMC,GAAG,GAAGP,cAAc,CAAoC,CAAC,CAAC,CAAC;EACjE,MAAMQ,QAAQ,GAAGR,cAAc,CAAgB,IAAI,CAAC;EAEpD,MAAMS,GAAG,GAAGjB,MAAM,CAAiC,IAAI,CAAC;EAExD,IAAI,CAACiB,GAAG,CAACC,OAAO,EAAE;IAChB,MAAMC,GAA4B,GAChCR,SAAS,IACN;MACH;MACA,IAAIA,SAAS,EAAE;QACb,MAAMS,mBAAmB,GAAGjB,QAAQ,CAAC,CAAC,GAClCD,2BAA2B,GAC3BG,cAAc;QAElB,MAAMgB,yBAAyB,GAAGA,CAAA,KAAM;UACtC,OAAOZ,MAAM,GACTC,wBAAwB,CAACC,SAAS,CAAC,GACnCS,mBAAmB,CAACV,wBAAwB,CAACC,SAAS,CAAC,CAAC;QAC9D,CAAC;QAEDI,GAAG,CAACO,KAAK,GAAGD,yBAAyB,CAAC,CAAC;;QAEvC;QACAF,GAAG,CAACI,MAAM,GAAGpB,QAAQ,CAAC,CAAC,GACnB,MAAME,cAAc,CAACK,wBAAwB,CAACC,SAAS,CAAC,CAAC,GACzDU,yBAAyB;QAE7BF,GAAG,CAACD,OAAO,GAAGP,SAAS;QACvB;QACA,IAAIV,QAAQ,CAACuB,EAAE,KAAK,KAAK,IAAI,CAACrB,QAAQ,CAAC,CAAC,EAAE;UACxCa,QAAQ,CAACM,KAAK,GACXX,SAAS,EAA+Bc,UAAU,EAC/CC,eAAe,IAAI,SAAS;QACpC;MACF;MACA,OAAOX,GAAG,CAACO,KAAK;IAClB,CAAE;IAEFH,GAAG,CAACD,OAAO,GAAG,IAAI;IAElB,MAAMS,0BAA0B,GAAGpB,2BAA2B,CAAC;MAC7DqB,MAAM,EAAEA,CAAA,KAAM;QACZ,SAAS;;QACT,MAAMC,CAAkB,GAAGA,CAAA,KAAMd,GAAG,CAACO,KAAK;QAC1CO,CAAC,CAACb,QAAQ,GAAGA,QAAQ;QACrB,OAAOa,CAAC;MACV;IACF,CAAC,CAAC;IACFvB,qBAAqB,CAACwB,GAAG,CAACX,GAAG,EAAEQ,0BAA0B,CAAC;IAC1DV,GAAG,CAACC,OAAO,GAAGC,GAAG;EACnB;EAEA,OAAOF,GAAG,CAACC,OAAO;AACpB", "ignoreList": []}