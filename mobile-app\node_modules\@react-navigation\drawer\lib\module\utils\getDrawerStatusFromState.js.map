{"version": 3, "names": ["getDrawerStatusFromState", "state", "history", "Error", "entry", "find", "it", "type", "status", "default"], "sourceRoot": "../../../src", "sources": ["utils/getDrawerStatusFromState.tsx"], "mappings": "AAMA,eAAe,SAASA,wBAAwB,CAC9CC,KAA2C,EAC7B;EACd,IAAIA,KAAK,CAACC,OAAO,IAAI,IAAI,EAAE;IACzB,MAAM,IAAIC,KAAK,CACb,sGAAsG,CACvG;EACH;EAEA,MAAMC,KAAK,GAAGH,KAAK,CAACC,OAAO,CAACG,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,IAAI,KAAK,QAAQ,CAEhD;EAEb,OAAO,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,MAAM,KAAIP,KAAK,CAACQ,OAAO,IAAI,QAAQ;AACnD"}