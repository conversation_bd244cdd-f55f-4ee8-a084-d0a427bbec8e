{"version": 3, "file": "withMod.js", "names": ["_chalk", "data", "_interopRequireDefault", "require", "_getenv", "_errors", "obj", "__esModule", "default", "EXPO_DEBUG", "boolish", "withBaseMod", "config", "platform", "mod", "action", "skipEmptyMod", "is<PERSON><PERSON><PERSON>", "isIntrospective", "saveToInternal", "mods", "intercepted<PERSON>od", "noopMod", "debugTrace", "isDebug", "_internal", "stack", "Error", "getDebugPluginStackFromStackTrace", "modStack", "chalk", "bold", "PluginError", "interceptingMod", "modRequest", "console", "log", "results", "nextMod", "saveToInternalObject", "modResults", "platformName", "modName", "stacktrace", "treeStackLines", "line", "split", "first", "second", "trim", "push", "plugins", "map", "match", "filter", "Boolean", "plugin", "includes", "commonPlugins", "reverse", "pluginName", "index", "toLowerCase", "red", "blue", "dim", "join", "with<PERSON><PERSON>"], "sources": ["../../src/plugins/withMod.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { JSONObject } from '@expo/json-file';\nimport chalk from 'chalk';\nimport { boolish } from 'getenv';\n\nimport { ExportedConfig, ExportedConfigWithProps, Mod, ModPlatform } from '../Plugin.types';\nimport { PluginError } from '../utils/errors';\n\nconst EXPO_DEBUG = boolish('EXPO_DEBUG', false);\n\nexport type BaseModOptions = {\n  /** Officially supports `'ios' | 'android'` (`ModPlatform`). Arbitrary strings are supported for adding out-of-tree platforms. */\n  platform: ModPlatform & string;\n  mod: string;\n  isProvider?: boolean;\n  skipEmptyMod?: boolean;\n  saveToInternal?: boolean;\n  /**\n   * If the mod supports introspection, and avoids making any filesystem modifications during compilation.\n   * By enabling, this mod, and all of its descendants will be run in introspection mode.\n   * This should only be used for static files like JSON or XML, and not for application files that require regexes,\n   * or complex static files that require other files to be generated like Xcode `.pbxproj`.\n   */\n  isIntrospective?: boolean;\n};\n\n/**\n * Plugin to intercept execution of a given `mod` with the given `action`.\n * If an action was already set on the given `config` config for `mod`, then it\n * will be provided to the `action` as `nextMod` when it's evaluated, otherwise\n * `nextMod` will be an identity function.\n *\n * @param config exported config\n * @param platform platform to target (ios or android)\n * @param mod name of the platform function to intercept\n * @param skipEmptyMod should skip running the action if there is no existing mod to intercept\n * @param saveToInternal should save the results to `_internal.modResults`, only enable this when the results are pure JSON.\n * @param isProvider should provide data up to the other mods.\n * @param action method to run on the mod when the config is compiled\n */\nexport function withBaseMod<T>(\n  config: ExportedConfig,\n  {\n    platform,\n    mod,\n    action,\n    skipEmptyMod,\n    isProvider,\n    isIntrospective,\n    saveToInternal,\n  }: BaseModOptions & { action: Mod<T> }\n): ExportedConfig {\n  if (!config.mods) {\n    config.mods = {};\n  }\n  if (!config.mods[platform]) {\n    config.mods[platform] = {};\n  }\n\n  let interceptedMod: Mod<T> = (config.mods[platform] as Record<string, any>)[mod];\n\n  // No existing mod to intercept\n  if (!interceptedMod) {\n    if (skipEmptyMod) {\n      // Skip running the action\n      return config;\n    }\n    // Use a noop mod and continue\n    const noopMod: Mod<T> = (config) => config;\n    interceptedMod = noopMod;\n  }\n\n  // Create a stack trace for debugging ahead of time\n  let debugTrace: string = '';\n  // Use the possibly user defined value. Otherwise fallback to the env variable.\n  // We support the env variable because user mods won't have _internal defined in time.\n  const isDebug = config._internal?.isDebug ?? EXPO_DEBUG;\n  if (isDebug) {\n    // Get a stack trace via the Error API\n    const stack = new Error().stack;\n    // Format the stack trace to create the debug log\n    debugTrace = getDebugPluginStackFromStackTrace(stack);\n    const modStack = chalk.bold(`${platform}.${mod}`);\n\n    debugTrace = `${modStack}: ${debugTrace}`;\n  }\n\n  // Prevent adding multiple providers to a mod.\n  // Base mods that provide files ignore any incoming modResults and therefore shouldn't have provider mods as parents.\n  if (interceptedMod.isProvider) {\n    if (isProvider) {\n      throw new PluginError(\n        `Cannot set provider mod for \"${platform}.${mod}\" because another is already being used.`,\n        'CONFLICTING_PROVIDER'\n      );\n    } else {\n      throw new PluginError(\n        `Cannot add mod to \"${platform}.${mod}\" because the provider has already been added. Provider must be the last mod added.`,\n        'INVALID_MOD_ORDER'\n      );\n    }\n  }\n\n  async function interceptingMod({ modRequest, ...config }: ExportedConfigWithProps<T>) {\n    if (isDebug) {\n      // In debug mod, log the plugin stack in the order which they were invoked\n      console.log(debugTrace);\n    }\n    const results = await action({\n      ...config,\n      modRequest: { ...modRequest, nextMod: interceptedMod },\n    });\n\n    if (saveToInternal) {\n      saveToInternalObject(results, platform, mod, results.modResults as unknown as JSONObject);\n    }\n    return results;\n  }\n\n  // Ensure this base mod is registered as the provider.\n  interceptingMod.isProvider = isProvider;\n\n  if (isIntrospective) {\n    // Register the mode as idempotent so introspection doesn't remove it.\n    interceptingMod.isIntrospective = isIntrospective;\n  }\n\n  (config.mods[platform] as any)[mod] = interceptingMod;\n\n  return config;\n}\n\nfunction saveToInternalObject(\n  config: Pick<ExpoConfig, '_internal'>,\n  platformName: ModPlatform,\n  modName: string,\n  results: JSONObject\n) {\n  if (!config._internal) config._internal = {};\n  if (!config._internal.modResults) config._internal.modResults = {};\n  if (!config._internal.modResults[platformName]) config._internal.modResults[platformName] = {};\n  config._internal.modResults[platformName][modName] = results;\n}\n\nfunction getDebugPluginStackFromStackTrace(stacktrace?: string): string {\n  if (!stacktrace) {\n    return '';\n  }\n\n  const treeStackLines: string[] = [];\n  for (const line of stacktrace.split('\\n')) {\n    const [first, second] = line.trim().split(' ');\n    if (first === 'at') {\n      treeStackLines.push(second);\n    }\n  }\n\n  const plugins = treeStackLines\n    .map((first) => {\n      // Match the first part of the stack trace against the plugin naming convention\n      // \"with\" followed by a capital letter.\n      return (\n        first?.match(/^(\\bwith[A-Z].*?\\b)/)?.[1]?.trim() ??\n        first?.match(/\\.(\\bwith[A-Z].*?\\b)/)?.[1]?.trim() ??\n        null\n      );\n    })\n    .filter(Boolean)\n    .filter((plugin) => {\n      // redundant as all debug logs are captured in withBaseMod\n      return !['withMod', 'withBaseMod', 'withExtendedMod'].includes(plugin!);\n    });\n\n  const commonPlugins = ['withPlugins', 'withRunOnce', 'withStaticPlugin'];\n\n  return (\n    (plugins as string[])\n      .reverse()\n      .map((pluginName, index) => {\n        // Base mods indicate a logical section.\n        if (pluginName.includes('BaseMod')) {\n          pluginName = chalk.bold(pluginName);\n        }\n        // highlight dangerous mods\n        if (pluginName.toLowerCase().includes('dangerous')) {\n          pluginName = chalk.red(pluginName);\n        }\n\n        if (index === 0) {\n          return chalk.blue(pluginName);\n        } else if (commonPlugins.includes(pluginName)) {\n          // Common mod names often clutter up the logs, dim them out\n          return chalk.dim(pluginName);\n        }\n        return pluginName;\n      })\n      // Join the results:\n      // withAndroidExpoPlugins ➜ withPlugins ➜ withIcons ➜ withDangerousMod ➜ withMod\n      .join(' ➜ ')\n  );\n}\n\n/**\n * Plugin to extend a mod function in the plugins config.\n *\n * @param config exported config\n * @param platform platform to target (ios or android)\n * @param mod name of the platform function to extend\n * @param action method to run on the mod when the config is compiled\n */\nexport function withMod<T>(\n  config: ExportedConfig,\n  {\n    platform,\n    mod,\n    action,\n  }: {\n    platform: ModPlatform;\n    mod: string;\n    action: Mod<T>;\n  }\n): ExportedConfig {\n  return withBaseMod(config, {\n    platform,\n    mod,\n    isProvider: false,\n    async action({ modRequest: { nextMod, ...modRequest }, modResults, ...config }) {\n      const results = await action({ modRequest, modResults: modResults as T, ...config });\n      return nextMod!(results as any);\n    },\n  });\n}\n"], "mappings": ";;;;;;;AAEA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA8C,SAAAC,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9C,MAAMG,UAAU,GAAG,IAAAC,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC;AAkB/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CACzBC,MAAsB,EACtB;EACEC,QAAQ;EACRC,GAAG;EACHC,MAAM;EACNC,YAAY;EACZC,UAAU;EACVC,eAAe;EACfC;AACmC,CAAC,EACtB;EAChB,IAAI,CAACP,MAAM,CAACQ,IAAI,EAAE;IAChBR,MAAM,CAACQ,IAAI,GAAG,CAAC,CAAC;EAClB;EACA,IAAI,CAACR,MAAM,CAACQ,IAAI,CAACP,QAAQ,CAAC,EAAE;IAC1BD,MAAM,CAACQ,IAAI,CAACP,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC5B;EAEA,IAAIQ,cAAsB,GAAIT,MAAM,CAACQ,IAAI,CAACP,QAAQ,CAAC,CAAyBC,GAAG,CAAC;;EAEhF;EACA,IAAI,CAACO,cAAc,EAAE;IACnB,IAAIL,YAAY,EAAE;MAChB;MACA,OAAOJ,MAAM;IACf;IACA;IACA,MAAMU,OAAe,GAAIV,MAAM,IAAKA,MAAM;IAC1CS,cAAc,GAAGC,OAAO;EAC1B;;EAEA;EACA,IAAIC,UAAkB,GAAG,EAAE;EAC3B;EACA;EACA,MAAMC,OAAO,GAAGZ,MAAM,CAACa,SAAS,EAAED,OAAO,IAAIf,UAAU;EACvD,IAAIe,OAAO,EAAE;IACX;IACA,MAAME,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC,CAACD,KAAK;IAC/B;IACAH,UAAU,GAAGK,iCAAiC,CAACF,KAAK,CAAC;IACrD,MAAMG,QAAQ,GAAGC,gBAAK,CAACC,IAAI,CAAC,GAAGlB,QAAQ,IAAIC,GAAG,EAAE,CAAC;IAEjDS,UAAU,GAAG,GAAGM,QAAQ,KAAKN,UAAU,EAAE;EAC3C;;EAEA;EACA;EACA,IAAIF,cAAc,CAACJ,UAAU,EAAE;IAC7B,IAAIA,UAAU,EAAE;MACd,MAAM,KAAIe,qBAAW,EACnB,gCAAgCnB,QAAQ,IAAIC,GAAG,0CAA0C,EACzF,sBACF,CAAC;IACH,CAAC,MAAM;MACL,MAAM,KAAIkB,qBAAW,EACnB,sBAAsBnB,QAAQ,IAAIC,GAAG,qFAAqF,EAC1H,mBACF,CAAC;IACH;EACF;EAEA,eAAemB,eAAeA,CAAC;IAAEC,UAAU;IAAE,GAAGtB;EAAmC,CAAC,EAAE;IACpF,IAAIY,OAAO,EAAE;MACX;MACAW,OAAO,CAACC,GAAG,CAACb,UAAU,CAAC;IACzB;IACA,MAAMc,OAAO,GAAG,MAAMtB,MAAM,CAAC;MAC3B,GAAGH,MAAM;MACTsB,UAAU,EAAE;QAAE,GAAGA,UAAU;QAAEI,OAAO,EAAEjB;MAAe;IACvD,CAAC,CAAC;IAEF,IAAIF,cAAc,EAAE;MAClBoB,oBAAoB,CAACF,OAAO,EAAExB,QAAQ,EAAEC,GAAG,EAAEuB,OAAO,CAACG,UAAmC,CAAC;IAC3F;IACA,OAAOH,OAAO;EAChB;;EAEA;EACAJ,eAAe,CAAChB,UAAU,GAAGA,UAAU;EAEvC,IAAIC,eAAe,EAAE;IACnB;IACAe,eAAe,CAACf,eAAe,GAAGA,eAAe;EACnD;EAECN,MAAM,CAACQ,IAAI,CAACP,QAAQ,CAAC,CAASC,GAAG,CAAC,GAAGmB,eAAe;EAErD,OAAOrB,MAAM;AACf;AAEA,SAAS2B,oBAAoBA,CAC3B3B,MAAqC,EACrC6B,YAAyB,EACzBC,OAAe,EACfL,OAAmB,EACnB;EACA,IAAI,CAACzB,MAAM,CAACa,SAAS,EAAEb,MAAM,CAACa,SAAS,GAAG,CAAC,CAAC;EAC5C,IAAI,CAACb,MAAM,CAACa,SAAS,CAACe,UAAU,EAAE5B,MAAM,CAACa,SAAS,CAACe,UAAU,GAAG,CAAC,CAAC;EAClE,IAAI,CAAC5B,MAAM,CAACa,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,EAAE7B,MAAM,CAACa,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;EAC9F7B,MAAM,CAACa,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,CAACC,OAAO,CAAC,GAAGL,OAAO;AAC9D;AAEA,SAAST,iCAAiCA,CAACe,UAAmB,EAAU;EACtE,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,EAAE;EACX;EAEA,MAAMC,cAAwB,GAAG,EAAE;EACnC,KAAK,MAAMC,IAAI,IAAIF,UAAU,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGH,IAAI,CAACI,IAAI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClBH,cAAc,CAACM,IAAI,CAACF,MAAM,CAAC;IAC7B;EACF;EAEA,MAAMG,OAAO,GAAGP,cAAc,CAC3BQ,GAAG,CAAEL,KAAK,IAAK;IACd;IACA;IACA,OACEA,KAAK,EAAEM,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAAC,CAAC,IAChDF,KAAK,EAAEM,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAAC,CAAC,IACjD,IAAI;EAER,CAAC,CAAC,CACDK,MAAM,CAACC,OAAO,CAAC,CACfD,MAAM,CAAEE,MAAM,IAAK;IAClB;IACA,OAAO,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,MAAO,CAAC;EACzE,CAAC,CAAC;EAEJ,MAAME,aAAa,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,kBAAkB,CAAC;EAExE,OACGP,OAAO,CACLQ,OAAO,CAAC,CAAC,CACTP,GAAG,CAAC,CAACQ,UAAU,EAAEC,KAAK,KAAK;IAC1B;IACA,IAAID,UAAU,CAACH,QAAQ,CAAC,SAAS,CAAC,EAAE;MAClCG,UAAU,GAAG9B,gBAAK,CAACC,IAAI,CAAC6B,UAAU,CAAC;IACrC;IACA;IACA,IAAIA,UAAU,CAACE,WAAW,CAAC,CAAC,CAACL,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClDG,UAAU,GAAG9B,gBAAK,CAACiC,GAAG,CAACH,UAAU,CAAC;IACpC;IAEA,IAAIC,KAAK,KAAK,CAAC,EAAE;MACf,OAAO/B,gBAAK,CAACkC,IAAI,CAACJ,UAAU,CAAC;IAC/B,CAAC,MAAM,IAAIF,aAAa,CAACD,QAAQ,CAACG,UAAU,CAAC,EAAE;MAC7C;MACA,OAAO9B,gBAAK,CAACmC,GAAG,CAACL,UAAU,CAAC;IAC9B;IACA,OAAOA,UAAU;EACnB,CAAC;EACD;EACA;EAAA,CACCM,IAAI,CAAC,KAAK,CAAC;AAElB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CACrBvD,MAAsB,EACtB;EACEC,QAAQ;EACRC,GAAG;EACHC;AAKF,CAAC,EACe;EAChB,OAAOJ,WAAW,CAACC,MAAM,EAAE;IACzBC,QAAQ;IACRC,GAAG;IACHG,UAAU,EAAE,KAAK;IACjB,MAAMF,MAAMA,CAAC;MAAEmB,UAAU,EAAE;QAAEI,OAAO;QAAE,GAAGJ;MAAW,CAAC;MAAEM,UAAU;MAAE,GAAG5B;IAAO,CAAC,EAAE;MAC9E,MAAMyB,OAAO,GAAG,MAAMtB,MAAM,CAAC;QAAEmB,UAAU;QAAEM,UAAU,EAAEA,UAAe;QAAE,GAAG5B;MAAO,CAAC,CAAC;MACpF,OAAO0B,OAAO,CAAED,OAAc,CAAC;IACjC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}