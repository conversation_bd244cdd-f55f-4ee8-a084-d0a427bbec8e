import React, {useEffect, useState} from 'react';
import {StatusBar, LogBox} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {Provider as PaperProvider} from 'react-native-paper';
import * as SplashScreen from 'expo-splash-screen';

import {AuthProvider} from './src/contexts/AuthContext';
import {CartProvider} from './src/contexts/CartContext';
import {NotificationProvider} from './src/contexts/NotificationContext';
import AppNavigator from './src/navigation/AppNavigator';
import {theme} from './src/styles/theme';
import LoadingScreen from './src/components/LoadingScreen';
import SimpleTestScreen from './SimpleTestScreen';

// Ignore specific warnings
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'VirtualizedLists should never be nested',
  'Warning: Cannot update a component',
]);

// Hide splash screen immediately
// SplashScreen.preventAutoHideAsync();

const App = () => {
  const [appIsReady, setAppIsReady] = useState(true); // Set to true to skip loading
  const [showTestLogin, setShowTestLogin] = useState(true); // Set to true to show test login

  useEffect(() => {
    async function prepare() {
      try {
        // Hide splash screen immediately
        await SplashScreen.hideAsync();
      } catch (e) {
        console.warn(e);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = React.useCallback(async () => {
    if (appIsReady) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return <LoadingScreen />;
  }

  // Show test login screen for debugging
  if (showTestLogin) {
    return (
      <PaperProvider theme={theme}>
        <StatusBar
          barStyle="light-content"
          backgroundColor={theme.colors.primary}
          translucent={false}
        />
        <SimpleTestScreen />
      </PaperProvider>
    );
  }

  return (
    <PaperProvider theme={theme}>
      <AuthProvider>
        <CartProvider>
          <NotificationProvider>
            <NavigationContainer onReady={onLayoutRootView}>
              <StatusBar
                barStyle="light-content"
                backgroundColor={theme.colors.primary}
                translucent={false}
              />
              <AppNavigator />
            </NavigationContainer>
          </NotificationProvider>
        </CartProvider>
      </AuthProvider>
    </PaperProvider>
  );
};

export default App;
