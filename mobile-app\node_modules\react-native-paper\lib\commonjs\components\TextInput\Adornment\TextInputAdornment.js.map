{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_enums", "_TextInputAffix", "_interopRequireWildcard", "_TextInputIcon", "_helpers", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "getAdornmentConfig", "left", "right", "adornmentConfig", "side", "AdornmentSide", "Left", "adornment", "Right", "for<PERSON>ach", "React", "isValidElement", "type", "TextInputAffix", "AdornmentType", "Affix", "TextInputIcon", "Icon", "push", "getAdornmentStyleAdjustmentForNativeInput", "leftAffixWidth", "rightAffix<PERSON>idth", "paddingHorizontal", "inputOffset", "mode", "isV3", "OUTLINED_INPUT_OFFSET", "ADORNMENT_OFFSET", "getConstants", "adornmentStyleAdjustmentForNativeInput", "map", "isLeftSide", "inputModeAdornemntOffset", "InputMode", "Outlined", "paddingKey", "captalize", "affix<PERSON>id<PERSON>", "padding", "offset", "isAffix", "margin<PERSON>ey", "allStyleAdjustmentsMerged", "reduce", "mergedStyles", "currentStyle", "text", "char<PERSON>t", "toUpperCase", "slice", "TextInputAdornment", "onAffixChange", "textStyle", "visible", "topPosition", "isTextInputFocused", "forceFocus", "maxFontSizeMultiplier", "theme", "disabled", "createElement", "Fragment", "inputAdornmentComponent", "commonProps", "testID", "IconAdornment", "key", "icon", "AffixAdornment", "affix", "onLayout", "_default", "exports"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Adornment/TextInputAdornment.tsx"], "mappings": ";;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAWA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,cAAA,GAAAD,uBAAA,CAAAH,OAAA;AAKA,IAAAK,QAAA,GAAAL,OAAA;AAA0C,SAAAG,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAR,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAK,UAAA,GAAAL,CAAA,KAAAU,OAAA,EAAAV,CAAA;AAAA,SAAAmB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAEnC,SAASG,kBAAkBA,CAAC;EACjCC,IAAI;EACJC;AAIF,CAAC,EAA0B;EACzB,IAAIC,eAAsB,GAAG,EAAE;EAC/B,IAAIF,IAAI,IAAIC,KAAK,EAAE;IACjB,CACE;MAAEE,IAAI,EAAEC,oBAAa,CAACC,IAAI;MAAEC,SAAS,EAAEN;IAAK,CAAC,EAC7C;MAAEG,IAAI,EAAEC,oBAAa,CAACG,KAAK;MAAED,SAAS,EAAEL;IAAM,CAAC,CAChD,CAACO,OAAO,CAAC,CAAC;MAAEL,IAAI;MAAEG;IAAU,CAAC,KAAK;MACjC,IAAIA,SAAS,iBAAIG,cAAK,CAACC,cAAc,CAACJ,SAAS,CAAC,EAAE;QAChD,IAAIK,IAAI;QACR,IAAIL,SAAS,CAACK,IAAI,KAAKC,uBAAc,EAAE;UACrCD,IAAI,GAAGE,oBAAa,CAACC,KAAK;QAC5B,CAAC,MAAM,IAAIR,SAAS,CAACK,IAAI,KAAKI,sBAAa,EAAE;UAC3CJ,IAAI,GAAGE,oBAAa,CAACG,IAAI;QAC3B;QACAd,eAAe,CAACe,IAAI,CAAC;UACnBd,IAAI;UACJQ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA,OAAOT,eAAe;AACxB;AAEO,SAASgB,yCAAyCA,CAAC;EACxDhB,eAAe;EACfiB,cAAc;EACdC,eAAe;EACfC,iBAAiB;EACjBC,WAAW,GAAG,CAAC;EACfC,IAAI;EACJC;AASF,CAAC,EAA+C;EAC9C,MAAM;IAAEC,qBAAqB;IAAEC;EAAiB,CAAC,GAAG,IAAAC,qBAAY,EAACH,IAAI,CAAC;EAEtE,IAAItB,eAAe,CAACL,MAAM,EAAE;IAC1B,MAAM+B,sCAAsC,GAAG1B,eAAe,CAAC2B,GAAG,CAChE,CAAC;MAAElB,IAAI;MAAER;IAAsB,CAAC,KAAK;MACnC,MAAM2B,UAAU,GAAG3B,IAAI,KAAKC,oBAAa,CAACC,IAAI;MAC9C,MAAM0B,wBAAwB,GAC5BR,IAAI,KAAKS,gBAAS,CAACC,QAAQ,GACvBP,gBAAgB,GAAGD,qBAAqB,GACxCC,gBAAgB;MACtB,MAAMQ,UAAU,GAAG,UAAUC,SAAS,CAAChC,IAAI,CAAC,EAAE;MAC9C,MAAMiC,UAAU,GAAGN,UAAU,GAAGX,cAAc,GAAGC,eAAe;MAChE,MAAMiB,OAAO,GACX,OAAOhB,iBAAiB,KAAK,QAAQ,GACjCA,iBAAiB,GACjBU,wBAAwB;MAC9B,MAAMO,MAAM,GAAGF,UAAU,GAAGC,OAAO;MAEnC,MAAME,OAAO,GAAG5B,IAAI,KAAKE,oBAAa,CAACC,KAAK;MAC5C,MAAM0B,SAAS,GAAG,SAASL,SAAS,CAAChC,IAAI,CAAC,EAAE;MAE5C,OAAO;QACL,CAACqC,SAAS,GAAGD,OAAO,GAAG,CAAC,GAAGD,MAAM;QACjC,CAACJ,UAAU,GAAGK,OAAO,GAAGD,MAAM,GAAGhB;MACnC,CAAC;IACH,CACF,CAAC;IACD,MAAMmB,yBAAyB,GAC7Bb,sCAAsC,CAACc,MAAM,CAC3C,CAACC,YAAY,EAAEC,YAAY,KAAK;MAC9B,OAAO;QACL,GAAGD,YAAY;QACf,GAAGC;MACL,CAAC;IACH,CAAC,EACD,CAAC,CACH,CAAC;IACH,OAAOH,yBAAyB;EAClC,CAAC,MAAM;IACL,OAAO,CAAC,CAAC,CAAC,CAAC;EACb;AACF;AAEA,MAAMN,SAAS,GAAIU,IAAY,IAC7BA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;AA2B9C,MAAMC,kBAAoE,GAAGA,CAAC;EAC5E/C,eAAe;EACfF,IAAI;EACJC,KAAK;EACLiD,aAAa;EACbC,SAAS;EACTC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,UAAU;EACVlC,iBAAiB;EACjBmC,qBAAqB;EACrBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,IAAIxD,eAAe,CAACL,MAAM,EAAE;IAC1B,oBACE/B,MAAA,CAAAkB,OAAA,CAAA2E,aAAA,CAAA7F,MAAA,CAAAkB,OAAA,CAAA4E,QAAA,QACG1D,eAAe,CAAC2B,GAAG,CAAC,CAAC;MAAElB,IAAI;MAAER;IAAsB,CAAC,KAAK;MACxD,IAAI0D,uBAAuB;MAC3B,IAAI1D,IAAI,KAAKC,oBAAa,CAACC,IAAI,EAAE;QAC/BwD,uBAAuB,GAAG7D,IAAI;MAChC,CAAC,MAAM,IAAIG,IAAI,KAAKC,oBAAa,CAACG,KAAK,EAAE;QACvCsD,uBAAuB,GAAG5D,KAAK;MACjC;MAEA,MAAM6D,WAAW,GAAG;QAClB3D,IAAI,EAAEA,IAAI;QACV4D,MAAM,EAAE,GAAG5D,IAAI,IAAIQ,IAAI,YAAY;QACnC2C,kBAAkB;QAClBjC,iBAAiB;QACjBqC;MACF,CAAC;MACD,IAAI/C,IAAI,KAAKE,oBAAa,CAACG,IAAI,EAAE;QAC/B,oBACElD,MAAA,CAAAkB,OAAA,CAAA2E,aAAA,CAACvF,cAAA,CAAA4F,aAAa,EAAAvE,QAAA,KACRqE,WAAW;UACfL,KAAK,EAAEA,KAAM;UACbQ,GAAG,EAAE9D,IAAK;UACV+D,IAAI,EAAEL,uBAAwB;UAC9BR,WAAW,EAAEA,WAAW,CAACxC,oBAAa,CAACG,IAAI,CAAE;UAC7CuC,UAAU,EAAEA;QAAW,EACxB,CAAC;MAEN,CAAC,MAAM,IAAI5C,IAAI,KAAKE,oBAAa,CAACC,KAAK,EAAE;QACvC,oBACEhD,MAAA,CAAAkB,OAAA,CAAA2E,aAAA,CAACzF,eAAA,CAAAiG,cAAc,EAAA1E,QAAA,KACTqE,WAAW;UACfG,GAAG,EAAE9D,IAAK;UACVkD,WAAW,EAAEA,WAAW,CAACxC,oBAAa,CAACC,KAAK,CAAC,CAACX,IAAI,CAAE;UACpDiE,KAAK,EAAEP,uBAAwB;UAC/BV,SAAS,EAAEA,SAAU;UACrBkB,QAAQ,EAAEnB,aAAa,CAAC/C,IAAI,CAAE;UAC9BiD,OAAO,EAAEA,OAAQ;UACjBI,qBAAqB,EAAEA;QAAsB,EAC9C,CAAC;MAEN,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CACD,CAAC;EAEP,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC;AAAC,IAAAc,QAAA,GAAAC,OAAA,CAAAvF,OAAA,GAEaiE,kBAAkB", "ignoreList": []}