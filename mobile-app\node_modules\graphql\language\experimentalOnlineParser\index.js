"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "OnlineParser", {
  enumerable: true,
  get: function get() {
    return _onlineParser.OnlineParser;
  }
});
Object.defineProperty(exports, "RuleKind", {
  enumerable: true,
  get: function get() {
    return _onlineParser.RuleKind;
  }
});
Object.defineProperty(exports, "TokenKind", {
  enumerable: true,
  get: function get() {
    return _onlineParser.TokenKind;
  }
});
Object.defineProperty(exports, "OnlineParserState", {
  enumerable: true,
  get: function get() {
    return _onlineParser.OnlineParserState;
  }
});

var _onlineParser = require("./onlineParser.js");
