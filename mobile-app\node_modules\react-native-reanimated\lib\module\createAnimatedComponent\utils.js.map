{"version": 3, "names": ["flattenArray", "array", "Array", "isArray", "resultArr", "_flattenArray", "arr", "for<PERSON>ach", "item", "push", "has", "key", "x", "undefined"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/utils.ts"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,YAAYA,CAAIC,KAAqB,EAAO;EAC1D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACzB,OAAO,CAACA,KAAK,CAAC;EAChB;EACA,MAAMG,SAAc,GAAG,EAAE;EAEzB,MAAMC,aAAa,GAAIC,GAAqB,IAAW;IACrDA,GAAG,CAACC,OAAO,CAAEC,IAAI,IAAK;MACpB,IAAIN,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,EAAE;QACvBH,aAAa,CAACG,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,SAAS,CAACK,IAAI,CAACD,IAAI,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;EACDH,aAAa,CAACJ,KAAK,CAAC;EACpB,OAAOG,SAAS;AAClB;AAEA,OAAO,MAAMM,GAAG,GAAGA,CACjBC,GAAM,EACNC,CAAU,KACuB;EACjC,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACpD,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAOF,GAAG,IAAIC,CAAC;IACjB;EACF;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}