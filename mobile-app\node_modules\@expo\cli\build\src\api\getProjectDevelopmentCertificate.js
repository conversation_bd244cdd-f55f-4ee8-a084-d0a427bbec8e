"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "getProjectDevelopmentCertificateAsync", {
    enumerable: true,
    get: ()=>getProjectDevelopmentCertificateAsync
});
const _client = require("./rest/client");
const _errors = require("../utils/errors");
async function getProjectDevelopmentCertificateAsync(easProjectId, csrPEM) {
    const response = await (0, _client.fetchAsync)(`projects/${encodeURIComponent(easProjectId)}/development-certificates`, {
        method: "POST",
        body: JSON.stringify({
            csrPEM
        })
    });
    if (!response.ok) {
        throw new _errors.CommandError("API", `Unexpected error from Expo servers: ${response.statusText}.`);
    }
    const buffer = await response.buffer();
    return buffer.toString("utf8");
}

//# sourceMappingURL=getProjectDevelopmentCertificate.js.map