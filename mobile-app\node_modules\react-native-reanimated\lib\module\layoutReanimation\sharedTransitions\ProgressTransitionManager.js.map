{"version": 3, "names": ["Platform", "registerEventHandler", "unregisterEventHandler", "ReanimatedError", "isJest", "shouldBeUseWeb", "runOnUIImmediately", "IS_ANDROID", "OS", "ProgressTransitionManager", "_sharedElementCount", "_event<PERSON><PERSON><PERSON>", "isRegistered", "onTransitionProgress", "onAppear", "onDisappear", "onSwipeDismiss", "addProgressAnimation", "viewTag", "progressAnimation", "global", "ProgressTransitionRegister", "registerEventHandlers", "removeProgressAnimation", "isUnmounting", "unregisterEventHandlers", "<PERSON><PERSON><PERSON><PERSON>", "eventPrefix", "lastProgressValue", "event", "progress", "frame", "onTransitionEnd", "onAndroidFinishTransitioning", "createProgressTransitionRegister", "progressAnimations", "Map", "snapshots", "currentTransitions", "Set", "toRemove", "skipCleaning", "isTransitionRestart", "progressTransitionManager", "size", "has", "set", "add", "delete", "onTransitionStart", "snapshot", "get", "removeViews", "clear", "_notifyAboutEnd", "maybeThrowError", "Proxy"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/sharedTransitions/ProgressTransitionManager.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,cAAc;AAMvC,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,eAAY;AACzE,SAASC,eAAe,QAAQ,iBAAc;AAC9C,SAASC,MAAM,EAAEC,cAAc,QAAQ,0BAAuB;AAC9D,SAASC,kBAAkB,QAAQ,kBAAe;AAUlD,MAAMC,UAAU,GAAGP,QAAQ,CAACQ,EAAE,KAAK,SAAS;AAE5C,OAAO,MAAMC,yBAAyB,CAAC;EAC7BC,mBAAmB,GAAG,CAAC;EACvBC,aAAa,GAAG;IACtBC,YAAY,EAAE,KAAK;IACnBC,oBAAoB,EAAE,CAAC,CAAC;IACxBC,QAAQ,EAAE,CAAC,CAAC;IACZC,WAAW,EAAE,CAAC,CAAC;IACfC,cAAc,EAAE,CAAC;EACnB,CAAC;EAEMC,oBAAoBA,CACzBC,OAAe,EACfC,iBAAoC,EACpC;IACAb,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTc,MAAM,CAACC,0BAA0B,CAACJ,oBAAoB,CACpDC,OAAO,EACPC,iBACF,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IAEJ,IAAI,CAACG,qBAAqB,CAAC,CAAC;EAC9B;EAEOC,uBAAuBA,CAACL,OAAe,EAAEM,YAAY,GAAG,IAAI,EAAE;IACnE,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9BnB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTc,MAAM,CAACC,0BAA0B,CAACE,uBAAuB,CACvDL,OAAO,EACPM,YACF,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;EACN;EAEQF,qBAAqBA,CAAA,EAAG;IAC9B,IAAI,CAACZ,mBAAmB,EAAE;IAC1B,MAAMgB,YAAY,GAAG,IAAI,CAACf,aAAa;IACvC,IAAI,CAACe,YAAY,CAACd,YAAY,EAAE;MAC9Bc,YAAY,CAACd,YAAY,GAAG,IAAI;MAChC,MAAMe,WAAW,GAAGpB,UAAU,GAAG,IAAI,GAAG,KAAK;MAC7C,IAAIqB,iBAAiB,GAAG,CAAC,CAAC;MAC1BF,YAAY,CAACb,oBAAoB,GAAGZ,oBAAoB,CACrD4B,KAA8B,IAAK;QAClC,SAAS;;QACT,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;QAC/B,IAAIA,QAAQ,KAAKF,iBAAiB,EAAE;UAClC;UACA;UACA;UACA;QACF;QACAA,iBAAiB,GAAGE,QAAQ;QAC5BV,MAAM,CAACC,0BAA0B,CAACU,KAAK,CAACD,QAAQ,CAAC;MACnD,CAAC,EACDH,WAAW,GAAG,oBAChB,CAAC;MACDD,YAAY,CAACZ,QAAQ,GAAGb,oBAAoB,CAAC,MAAM;QACjD,SAAS;;QACTmB,MAAM,CAACC,0BAA0B,CAACW,eAAe,CAAC,CAAC;MACrD,CAAC,EAAEL,WAAW,GAAG,QAAQ,CAAC;MAE1B,IAAIpB,UAAU,EAAE;QACd;QACA;QACAmB,YAAY,CAACX,WAAW,GAAGd,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTmB,MAAM,CAACC,0BAA0B,CAACY,4BAA4B,CAAC,CAAC;QAClE,CAAC,EAAE,uBAAuB,CAAC;MAC7B,CAAC,MAAM,IAAIjC,QAAQ,CAACQ,EAAE,KAAK,KAAK,EAAE;QAChC;QACAkB,YAAY,CAACX,WAAW,GAAGd,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTmB,MAAM,CAACC,0BAA0B,CAACW,eAAe,CAAC,IAAI,CAAC;QACzD,CAAC,EAAE,cAAc,CAAC;QAClBN,YAAY,CAACV,cAAc,GAAGf,oBAAoB,CAAC,MAAM;UACvD,SAAS;;UACTmB,MAAM,CAACC,0BAA0B,CAACW,eAAe,CAAC,CAAC;QACrD,CAAC,EAAE,kBAAkB,CAAC;MACxB;IACF;EACF;EAEQP,uBAAuBA,CAAA,EAAS;IACtC,IAAI,CAACf,mBAAmB,EAAE;IAC1B,IAAI,IAAI,CAACA,mBAAmB,KAAK,CAAC,EAAE;MAClC,MAAMgB,YAAY,GAAG,IAAI,CAACf,aAAa;MACvCe,YAAY,CAACd,YAAY,GAAG,KAAK;MACjC,IAAIc,YAAY,CAACb,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC5CX,sBAAsB,CAACwB,YAAY,CAACb,oBAAoB,CAAC;QACzDa,YAAY,CAACb,oBAAoB,GAAG,CAAC,CAAC;MACxC;MACA,IAAIa,YAAY,CAACZ,QAAQ,KAAK,CAAC,CAAC,EAAE;QAChCZ,sBAAsB,CAACwB,YAAY,CAACZ,QAAQ,CAAC;QAC7CY,YAAY,CAACZ,QAAQ,GAAG,CAAC,CAAC;MAC5B;MACA,IAAIY,YAAY,CAACX,WAAW,KAAK,CAAC,CAAC,EAAE;QACnCb,sBAAsB,CAACwB,YAAY,CAACX,WAAW,CAAC;QAChDW,YAAY,CAACX,WAAW,GAAG,CAAC,CAAC;MAC/B;MACA,IAAIW,YAAY,CAACV,cAAc,KAAK,CAAC,CAAC,EAAE;QACtCd,sBAAsB,CAACwB,YAAY,CAACV,cAAc,CAAC;QACnDU,YAAY,CAACV,cAAc,GAAG,CAAC,CAAC;MAClC;IACF;EACF;AACF;AAEA,SAASkB,gCAAgCA,CAAA,EAAG;EAC1C,SAAS;;EACT,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAA4B,CAAC;EAC/D,MAAMC,SAAS,GAAG,IAAID,GAAG,CAGvB,CAAC;EACH,MAAME,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAS,CAAC;EAElC,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAE/B,MAAMC,yBAAyB,GAAG;IAChC1B,oBAAoB,EAAEA,CACpBC,OAAe,EACfC,iBAAoC,KACjC;MACH,IAAImB,kBAAkB,CAACM,IAAI,GAAG,CAAC,IAAI,CAACT,kBAAkB,CAACU,GAAG,CAAC3B,OAAO,CAAC,EAAE;QACnE;QACAwB,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA4B,kBAAkB,CAACW,GAAG,CAAC5B,OAAO,EAAEC,iBAAiB,CAAC;IACpD,CAAC;IACDI,uBAAuB,EAAEA,CAACL,OAAe,EAAEM,YAAqB,KAAK;MACnE,IAAIc,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA,IAAIiB,YAAY,EAAE;QAChB;QACAgB,QAAQ,CAACO,GAAG,CAAC7B,OAAO,CAAC;MACvB,CAAC,MAAM;QACL;QACAiB,kBAAkB,CAACa,MAAM,CAAC9B,OAAO,CAAC;MACpC;IACF,CAAC;IACD+B,iBAAiB,EAAEA,CACjB/B,OAAe,EACfgC,QAAmD,KAChD;MACHT,YAAY,GAAGC,mBAAmB;MAClCL,SAAS,CAACS,GAAG,CAAC5B,OAAO,EAAEgC,QAAQ,CAAC;MAChCZ,kBAAkB,CAACS,GAAG,CAAC7B,OAAO,CAAC;MAC/B;MACAyB,yBAAyB,CAACZ,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IACDA,KAAK,EAAGD,QAAgB,IAAK;MAC3B,KAAK,MAAMZ,OAAO,IAAIoB,kBAAkB,EAAE;QACxC,MAAMnB,iBAAiB,GAAGgB,kBAAkB,CAACgB,GAAG,CAACjC,OAAO,CAAC;QACzD,IAAI,CAACC,iBAAiB,EAAE;UACtB;QACF;QACA,MAAM+B,QAAQ,GAAGb,SAAS,CAACc,GAAG,CAC5BjC,OACF,CAAsC;QACtCC,iBAAiB,CAACD,OAAO,EAAEgC,QAAQ,EAAEpB,QAAQ,CAAC;MAChD;IACF,CAAC;IACDG,4BAA4B,EAAEA,CAAA,KAAM;MAClC,IAAIO,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB;QACAD,yBAAyB,CAACX,eAAe,CAAC,CAAC;MAC7C;IACF,CAAC;IACDA,eAAe,EAAEA,CAACoB,WAAW,GAAG,KAAK,KAAK;MACxC,IAAId,kBAAkB,CAACM,IAAI,KAAK,CAAC,EAAE;QACjCJ,QAAQ,CAACa,KAAK,CAAC,CAAC;QAChB;MACF;MACA,IAAIZ,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QACpBC,mBAAmB,GAAG,KAAK;QAC3B;MACF;MACA,KAAK,MAAMxB,OAAO,IAAIoB,kBAAkB,EAAE;QACxClB,MAAM,CAACkC,eAAe,CAACpC,OAAO,EAAEkC,WAAW,CAAC;MAC9C;MACAd,kBAAkB,CAACe,KAAK,CAAC,CAAC;MAC1B,IAAIX,mBAAmB,EAAE;QACvB;QACA;QACA;MACF;MACAL,SAAS,CAACgB,KAAK,CAAC,CAAC;MACjB,IAAIb,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB,KAAK,MAAM1B,OAAO,IAAIsB,QAAQ,EAAE;UAC9BL,kBAAkB,CAACa,MAAM,CAAC9B,OAAO,CAAC;UAClCE,MAAM,CAACkC,eAAe,CAACpC,OAAO,EAAEkC,WAAW,CAAC;QAC9C;QACAZ,QAAQ,CAACa,KAAK,CAAC,CAAC;MAClB;IACF;EACF,CAAC;EACD,OAAOV,yBAAyB;AAClC;AAEA,IAAItC,cAAc,CAAC,CAAC,EAAE;EACpB,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAACnD,MAAM,CAAC,CAAC,EAAE;MACb,MAAM,IAAID,eAAe,CACvB,uEACF,CAAC;IACH;EACF,CAAC;EACDiB,MAAM,CAACC,0BAA0B,GAAG,IAAImC,KAAK,CAC3C,CAAC,CAAC,EACF;IACEL,GAAG,EAAEI,eAAe;IACpBT,GAAG,EAAEA,CAAA,KAAM;MACTS,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CACF,CAAC;AACH,CAAC,MAAM;EACLjD,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTc,MAAM,CAACC,0BAA0B,GAAGa,gCAAgC,CAAC,CAAC;EACxE,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}