{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "nandinihub-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#FF6B35"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nandinihub.mobile", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "This app uses camera to scan products and take profile pictures", "NSPhotoLibraryUsageDescription": "This app uses photo library to select images for profile and reviews", "NSLocationWhenInUseUsageDescription": "This app uses location to provide better delivery experience"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.nandinihub.mobile", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "VIBRATE", "RECEIVE_BOOT_COMPLETED"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-splash-screen", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "sounds": ["./assets/notification-sound.wav"]}]], "extra": {"eas": {"projectId": "nandinihub-mobile-project"}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}