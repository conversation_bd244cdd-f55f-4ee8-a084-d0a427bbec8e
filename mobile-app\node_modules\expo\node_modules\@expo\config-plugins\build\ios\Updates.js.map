{"version": 3, "file": "Updates.js", "names": ["_iosPlugins", "data", "require", "_Updates", "_warnings", "Config", "exports", "withUpdates", "config", "withExpoPlist", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfigAsync", "expoPlist", "checkOnLaunch", "getUpdatesCheckOnLaunch", "timeout", "getUpdatesTimeout", "useEmbeddedUpdate", "getUpdatesUseEmbeddedUpdate", "addWarningIOS", "newExpoPlist", "ENABLED", "getUpdatesEnabled", "CHECK_ON_LAUNCH", "LAUNCH_WAIT_MS", "UPDATES_HAS_EMBEDDED_UPDATE", "updateUrl", "getUpdateUrl", "UPDATE_URL", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadata", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeaders", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "setVersionsConfigAsync", "runtimeVersion", "getRuntimeVersionNullableAsync", "RUNTIME_VERSION", "Error"], "sources": ["../../src/ios/Updates.ts"], "sourcesContent": ["import { ExpoPlist } from './IosConfig.types';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withExpoPlist } from '../plugins/ios-plugins';\nimport {\n  ExpoConfigUpdates,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullableAsync,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadata,\n  getUpdatesRequestHeaders,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdatesUseEmbeddedUpdate,\n  getUpdateUrl,\n} from '../utils/Updates';\nimport { addWarningIOS } from '../utils/warnings';\n\nexport enum Config {\n  ENABLED = 'EXUpdatesEnabled',\n  CHECK_ON_LAUNCH = 'EXUpdatesCheckOnLaunch',\n  LAUNCH_WAIT_MS = 'EXUpdatesLaunchWaitMs',\n  RUNTIME_VERSION = 'EXUpdatesRuntimeVersion',\n  UPDATE_URL = 'EXUpdatesURL',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'EXUpdatesRequestHeaders',\n  UPDATES_HAS_EMBEDDED_UPDATE = 'EXUpdatesHasEmbeddedUpdate',\n  CODE_SIGNING_CERTIFICATE = 'EXUpdatesCodeSigningCertificate',\n  CODE_SIGNING_METADATA = 'EXUpdatesCodeSigningMetadata',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin = (config) => {\n  return withExpoPlist(config, async (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = await setUpdatesConfigAsync(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nexport async function setUpdatesConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist,\n  expoUpdatesPackageVersion?: string | null\n): Promise<ExpoPlist> {\n  const checkOnLaunch = getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion);\n  const timeout = getUpdatesTimeout(config);\n  const useEmbeddedUpdate = getUpdatesUseEmbeddedUpdate(config);\n\n  // TODO: is there a better place for this validation?\n  if (!useEmbeddedUpdate && timeout === 0 && checkOnLaunch !== 'ALWAYS') {\n    addWarningIOS(\n      'updates.useEmbeddedUpdate',\n      `updates.checkOnLaunch should be set to \"ON_LOAD\" and updates.fallbackToCacheTimeout should be set to a non-zero value when updates.useEmbeddedUpdate is set to false. This is because an update must be fetched on the initial launch, when no embedded update is available.`\n    );\n  }\n\n  const newExpoPlist = {\n    ...expoPlist,\n    [Config.ENABLED]: getUpdatesEnabled(config),\n    [Config.CHECK_ON_LAUNCH]: checkOnLaunch,\n    [Config.LAUNCH_WAIT_MS]: timeout,\n  };\n\n  // The native config name is \"has embedded update\", but we want to expose\n  // this to the user as \"use embedded update\", since this is more accurate.\n  // The field does not disable actually building and embedding the update,\n  // only whether it is actually used.\n  if (useEmbeddedUpdate) {\n    delete newExpoPlist[Config.UPDATES_HAS_EMBEDDED_UPDATE];\n  } else {\n    newExpoPlist[Config.UPDATES_HAS_EMBEDDED_UPDATE] = false;\n  }\n\n  const updateUrl = getUpdateUrl(config);\n  if (updateUrl) {\n    newExpoPlist[Config.UPDATE_URL] = updateUrl;\n  } else {\n    delete newExpoPlist[Config.UPDATE_URL];\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    newExpoPlist[Config.CODE_SIGNING_CERTIFICATE] = codeSigningCertificate;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_CERTIFICATE];\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadata(config);\n  if (codeSigningMetadata) {\n    newExpoPlist[Config.CODE_SIGNING_METADATA] = codeSigningMetadata;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_METADATA];\n  }\n\n  const requestHeaders = getUpdatesRequestHeaders(config);\n  if (requestHeaders) {\n    newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY] = requestHeaders;\n  } else {\n    delete newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY];\n  }\n\n  return await setVersionsConfigAsync(projectRoot, config, newExpoPlist);\n}\n\nexport async function setVersionsConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist\n): Promise<ExpoPlist> {\n  const newExpoPlist = { ...expoPlist };\n\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'ios');\n  if (!runtimeVersion && expoPlist[Config.RUNTIME_VERSION]) {\n    throw new Error(\n      'A runtime version is set in your Expo.plist, but is missing from your app.json/app.config.js. Please either set runtimeVersion in your app.json/app.config.js or remove EXUpdatesRuntimeVersion from your Expo.plist.'\n    );\n  }\n\n  if (runtimeVersion) {\n    delete newExpoPlist['EXUpdatesSDKVersion'];\n    newExpoPlist[Config.RUNTIME_VERSION] = runtimeVersion;\n  } else {\n    delete newExpoPlist['EXUpdatesSDKVersion'];\n    delete newExpoPlist[Config.RUNTIME_VERSION];\n  }\n\n  return newExpoPlist;\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAG,UAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,IAEtCI,MAAM,GAAAC,OAAA,CAAAD,MAAA,0BAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAAA,OAANA,MAAM;AAAA,OAYlB;AACA;AAEO,MAAME,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,2BAAa,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC7C,MAAME,WAAW,GAAGF,MAAM,CAACG,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EF,MAAM,CAACM,UAAU,GAAG,MAAMC,qBAAqB,CAC7CL,WAAW,EACXF,MAAM,EACNA,MAAM,CAACM,UAAU,EACjBF,yBACF,CAAC;IACD,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACF,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEK,eAAeQ,qBAAqBA,CACzCL,WAAmB,EACnBF,MAAyB,EACzBQ,SAAoB,EACpBJ,yBAAyC,EACrB;EACpB,MAAMK,aAAa,GAAG,IAAAC,kCAAuB,EAACV,MAAM,EAAEI,yBAAyB,CAAC;EAChF,MAAMO,OAAO,GAAG,IAAAC,4BAAiB,EAACZ,MAAM,CAAC;EACzC,MAAMa,iBAAiB,GAAG,IAAAC,sCAA2B,EAACd,MAAM,CAAC;;EAE7D;EACA,IAAI,CAACa,iBAAiB,IAAIF,OAAO,KAAK,CAAC,IAAIF,aAAa,KAAK,QAAQ,EAAE;IACrE,IAAAM,yBAAa,EACX,2BAA2B,EAC3B,8QACF,CAAC;EACH;EAEA,MAAMC,YAAY,GAAG;IACnB,GAAGR,SAAS;IACZ,CAACX,MAAM,CAACoB,OAAO,GAAG,IAAAC,4BAAiB,EAAClB,MAAM,CAAC;IAC3C,CAACH,MAAM,CAACsB,eAAe,GAAGV,aAAa;IACvC,CAACZ,MAAM,CAACuB,cAAc,GAAGT;EAC3B,CAAC;;EAED;EACA;EACA;EACA;EACA,IAAIE,iBAAiB,EAAE;IACrB,OAAOG,YAAY,CAACnB,MAAM,CAACwB,2BAA2B,CAAC;EACzD,CAAC,MAAM;IACLL,YAAY,CAACnB,MAAM,CAACwB,2BAA2B,CAAC,GAAG,KAAK;EAC1D;EAEA,MAAMC,SAAS,GAAG,IAAAC,uBAAY,EAACvB,MAAM,CAAC;EACtC,IAAIsB,SAAS,EAAE;IACbN,YAAY,CAACnB,MAAM,CAAC2B,UAAU,CAAC,GAAGF,SAAS;EAC7C,CAAC,MAAM;IACL,OAAON,YAAY,CAACnB,MAAM,CAAC2B,UAAU,CAAC;EACxC;EAEA,MAAMC,sBAAsB,GAAG,IAAAC,2CAAgC,EAACxB,WAAW,EAAEF,MAAM,CAAC;EACpF,IAAIyB,sBAAsB,EAAE;IAC1BT,YAAY,CAACnB,MAAM,CAAC8B,wBAAwB,CAAC,GAAGF,sBAAsB;EACxE,CAAC,MAAM;IACL,OAAOT,YAAY,CAACnB,MAAM,CAAC8B,wBAAwB,CAAC;EACtD;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,wCAA6B,EAAC7B,MAAM,CAAC;EACjE,IAAI4B,mBAAmB,EAAE;IACvBZ,YAAY,CAACnB,MAAM,CAACiC,qBAAqB,CAAC,GAAGF,mBAAmB;EAClE,CAAC,MAAM;IACL,OAAOZ,YAAY,CAACnB,MAAM,CAACiC,qBAAqB,CAAC;EACnD;EAEA,MAAMC,cAAc,GAAG,IAAAC,mCAAwB,EAAChC,MAAM,CAAC;EACvD,IAAI+B,cAAc,EAAE;IAClBf,YAAY,CAACnB,MAAM,CAACoC,yCAAyC,CAAC,GAAGF,cAAc;EACjF,CAAC,MAAM;IACL,OAAOf,YAAY,CAACnB,MAAM,CAACoC,yCAAyC,CAAC;EACvE;EAEA,OAAO,MAAMC,sBAAsB,CAAChC,WAAW,EAAEF,MAAM,EAAEgB,YAAY,CAAC;AACxE;AAEO,eAAekB,sBAAsBA,CAC1ChC,WAAmB,EACnBF,MAAyB,EACzBQ,SAAoB,EACA;EACpB,MAAMQ,YAAY,GAAG;IAAE,GAAGR;EAAU,CAAC;EAErC,MAAM2B,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAAClC,WAAW,EAAEF,MAAM,EAAE,KAAK,CAAC;EACvF,IAAI,CAACmC,cAAc,IAAI3B,SAAS,CAACX,MAAM,CAACwC,eAAe,CAAC,EAAE;IACxD,MAAM,IAAIC,KAAK,CACb,uNACF,CAAC;EACH;EAEA,IAAIH,cAAc,EAAE;IAClB,OAAOnB,YAAY,CAAC,qBAAqB,CAAC;IAC1CA,YAAY,CAACnB,MAAM,CAACwC,eAAe,CAAC,GAAGF,cAAc;EACvD,CAAC,MAAM;IACL,OAAOnB,YAAY,CAAC,qBAAqB,CAAC;IAC1C,OAAOA,YAAY,CAACnB,MAAM,CAACwC,eAAe,CAAC;EAC7C;EAEA,OAAOrB,YAAY;AACrB", "ignoreList": []}