{"version": 3, "file": "versions.js", "names": ["_semver", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "gteSdkVersion", "exp", "sdkVersion", "semver", "gte", "Error", "lteSdkVersion", "lte"], "sources": ["../../src/utils/versions.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\nimport semver from 'semver';\n\nexport function gteSdkVersion(exp: Pick<ExpoConfig, 'sdkVersion'>, sdkVersion: string): boolean {\n  if (!exp.sdkVersion) {\n    return false;\n  }\n\n  if (exp.sdkVersion === 'UNVERSIONED') {\n    return true;\n  }\n\n  try {\n    return semver.gte(exp.sdkVersion, sdkVersion);\n  } catch {\n    throw new Error(`${exp.sdkVersion} is not a valid version. Must be in the form of x.y.z`);\n  }\n}\n\nexport function lteSdkVersion(exp: Pick<ExpoConfig, 'sdkVersion'>, sdkVersion: string): boolean {\n  if (!exp.sdkVersion) {\n    return false;\n  }\n\n  if (exp.sdkVersion === 'UNVERSIONED') {\n    return false;\n  }\n\n  try {\n    return semver.lte(exp.sdkVersion, sdkVersion);\n  } catch {\n    throw new Error(`${exp.sdkVersion} is not a valid version. Must be in the form of x.y.z`);\n  }\n}\n"], "mappings": ";;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErB,SAASG,aAAaA,CAACC,GAAmC,EAAEC,UAAkB,EAAW;EAC9F,IAAI,CAACD,GAAG,CAACC,UAAU,EAAE;IACnB,OAAO,KAAK;EACd;EAEA,IAAID,GAAG,CAACC,UAAU,KAAK,aAAa,EAAE;IACpC,OAAO,IAAI;EACb;EAEA,IAAI;IACF,OAAOC,iBAAM,CAACC,GAAG,CAACH,GAAG,CAACC,UAAU,EAAEA,UAAU,CAAC;EAC/C,CAAC,CAAC,MAAM;IACN,MAAM,IAAIG,KAAK,CAAC,GAAGJ,GAAG,CAACC,UAAU,uDAAuD,CAAC;EAC3F;AACF;AAEO,SAASI,aAAaA,CAACL,GAAmC,EAAEC,UAAkB,EAAW;EAC9F,IAAI,CAACD,GAAG,CAACC,UAAU,EAAE;IACnB,OAAO,KAAK;EACd;EAEA,IAAID,GAAG,CAACC,UAAU,KAAK,aAAa,EAAE;IACpC,OAAO,KAAK;EACd;EAEA,IAAI;IACF,OAAOC,iBAAM,CAACI,GAAG,CAACN,GAAG,CAACC,UAAU,EAAEA,UAAU,CAAC;EAC/C,CAAC,CAAC,MAAM;IACN,MAAM,IAAIG,KAAK,CAAC,GAAGJ,GAAG,CAACC,UAAU,uDAAuD,CAAC;EAC3F;AACF", "ignoreList": []}