{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/standalone/index.ts"], "names": [], "mappings": ";;AAGA,oDAAkG;AAClG,kDAA0F;AAE1F,SAAS,cAAc,CACrB,GAAY,EACZ,UAA2D;IAE3D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAA;IAC1E,CAAC;IACD,MAAM,EAAC,EAAE,EAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAA;IAC3B,OAAO,OAAO,UAAU,IAAI,UAAU;QACpC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC;QACnC,CAAC,CAAC,UAAU,KAAK,SAAS;YAC1B,CAAC,CAAC,gBAAgB,CAAS,UAAU,EAAE,WAAW,CAAC;YACnD,CAAC,CAAC,gBAAgB,CAAY,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAC/C,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAC/C,CAAA;IAEL,SAAS,WAAW,CAAC,EAAU;QAC7B,MAAM,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QAC3B,IAAI,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAA;QAC9D,OAAO,CAAC,CAAA;IACV,CAAC;IAED,SAAS,cAAc,CAAC,MAAmB;QACzC,MAAM,UAAU,GAAoB,EAAE,CAAA;QACtC,MAAM,CAAC,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,CAAA;QAC9B,MAAM,KAAK,GAAG,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;QAC9C,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACtB,iHAAiH;YACjH,OAAO,gBAAgB,EAAE,2BAA2B,CAAC,IAAI,EAAE,kBAAkB,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,CAAA;QAChG,CAAC;QACD,OAAO,gBAAgB,EAAE,oBAAoB,CAAC,IAAI,EAAE,4BAA4B,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,CAAA;IACnG,CAAC;IAED,SAAS,gBAAgB,CACvB,OAA4B,EAC5B,eAAgE;;QAEhE,MAAM,UAAU,GAAoB,EAAE,CAAA;QACtC,IAAI,IAAI,GAAG,IAAA,QAAC,EAAA,eAAe,CAAA;QAC3B,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,CAAM,CAAC,CAAA;YAC7C,IAAI,CAAC,EAAE,CAAC;gBACN,MAAM,KAAK,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;gBAChD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;oBACpC,CAAC,CAAC,IAAA,QAAC,EAAA,gBAAgB,IAAA,uBAAgB,EAAC,IAAI,CAAC,EAAE;oBAC3C,CAAC,CAAC,IAAA,QAAC,EAAA,UAAU,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,CAAA;gBAClC,IAAI,GAAG,IAAA,QAAC,EAAA,GAAG,IAAI,GAAG,EAAE,GAAG,YAAY,MAAM,MAAA,CAAC,CAAC,MAAM,0CAAE,YAAY,IAAI,EAAE,GAAG,KAAK,EAAE,CAAA;YACjF,CAAC;QACH,CAAC;QACD,OAAO,GAAG,IAAI,EAAE,CAAA;IAClB,CAAC;IAED,SAAS,YAAY,CAAC,UAA2B,EAAE,CAAc;QAC/D,IAAI,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;QAC/E,IAAI,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,sBAAc,CAAC,SAAS;YAAE,OAAO,UAAG,CAAA;QACtE,YAAY,CAAC,CAAC,CAAC,YAAY,EAAE,sBAAc,CAAC,OAAO,CAAC,CAAA;QAEpD,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;QACjF,MAAM,IAAI,GAAG,IAAI,YAAK,CAAC,GAAG,SAAS,GAAG,EAAE,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAA;QAC5D,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,QAAC,EAAA,GAAG,IAAI,GAAG,CAAC,CAAC,YAAY,gBAAgB,CAAC,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;QAExF,SAAS,eAAe,CAAC,CAAiB;;YACxC,MAAM,IAAI,GAAG,MAAA,CAAC,CAAC,KAAK,0CAAE,GAAG,CAAA;YACzB,IAAI,CAAC,CAAC,MAAM,KAAK,UAAU,IAAI,OAAO,IAAI,IAAI,UAAU,EAAE,CAAC;gBACzD,MAAM,CAAC,GAAG,IAA2B,CAAA;gBACrC,OAAO,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;YAC3C,CAAC;iBAAM,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,IAAI,OAAO,IAAI,IAAI,QAAQ,EAAE,CAAC;gBACtF,MAAM,EAAC,QAAQ,EAAE,YAAY,EAAC,GAAG,IAAiB,CAAA;gBAClD,IAAI,CAAC,YAAY;oBAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;gBACxD,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAQ,CAAC,KAAK,CAAA;gBAC7D,MAAM,OAAO,GAAG,IAAA,QAAC,EAAA,GAAG,GAAG,IAAI,CAAC,iBAAiB,YAAY,IAAI,CAAA;gBAC7D,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,sBAAc,CAAC,OAAO;oBAAE,OAAO,OAAO,CAAA;gBACtE,MAAM,KAAK,GAAG,YAAY,CAAC,UAAU,EAAE,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAC,CAAA;gBACxD,OAAO,IAAA,QAAC,EAAA,GAAG,OAAO,GAAG,EAAE,GAAG,KAAK,EAAE,CAAA;YACnC,CAAC;YACD,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,SAAS,SAAS,CAAC,IAAoB;;YACrC,OAAO,MAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,0CAAE,GAAG,CAAC,IAAI,CAAC,CAAA;QAC3C,CAAC;QAED,SAAS,YAAY,CAAC,IAAoB,EAAE,KAAqB;YAC/D,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAA;YACrB,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;YACpE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,cAAc,CAAA;AACzC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,EAAC,KAAK,EAAE,IAAI,EAAC,CAAC,CAAA;AAE3D,kBAAe,cAAc,CAAA"}