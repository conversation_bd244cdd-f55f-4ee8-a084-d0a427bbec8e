{"url": "http://localhost/tiffine/index.php/", "method": "GET", "isAJAX": false, "startTime": **********.073404, "totalTime": 3740.5, "totalMemory": "7.449", "segmentDuration": 535, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.27176, "duration": 1.****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.502894, "duration": 0.****************}, {"name": "Routing", "component": "Timer", "start": **********.663621, "duration": 0.*****************}, {"name": "Before Filters", "component": "Timer", "start": **********.724036, "duration": 2.9087066650390625e-05}, {"name": "Controller", "component": "Timer", "start": **********.72407, "duration": 2.***************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.72407, "duration": 0.****************}, {"name": "After Filters", "component": "Timer", "start": **********.7668, "duration": 7.152557373046875e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.766851, "duration": 0.*****************}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(11 total Queries, 10 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "15.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `banners`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\BannerModel.php:83", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:26", "function": "        App\\Models\\BannerModel->getSliderBanners()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\BannerModel.php:83", "qid": "58274e23f1ead72d8001089c68e301c0"}, {"hover": "", "class": "", "duration": "9.79 ms", "sql": "<strong>SELECT</strong> `products`.*, `categories`.`name` as `category_name`\n<strong>FROM</strong> `products`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `products`.`category_id`\n<strong>WHERE</strong> `products`.`is_featured` = 1\n<strong>AND</strong> `products`.`is_active` = 1\n<strong>AND</strong> `categories`.`is_active` = 1\n <strong>LIMIT</strong> 8", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\ProductModel.php:98", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:27", "function": "        App\\Models\\ProductModel->getFeaturedProducts()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\ProductModel.php:98", "qid": "2691e8a8af4df0a184964922637efe83"}, {"hover": "", "class": "", "duration": "7.75 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\CategoryModel.php:63", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:28", "function": "        App\\Models\\CategoryModel->getActiveCategories()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\CategoryModel.php:63", "qid": "15cc96220f706f814c4ecd6a94cefe1a"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> `products`.*, `categories`.`name` as `category_name`\n<strong>FROM</strong> `products`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `products`.`category_id`\n<strong>WHERE</strong> `products`.`is_active` = 1\n<strong>AND</strong> `categories`.`is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `products`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 12", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\ProductModel.php:87", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:29", "function": "        App\\Models\\ProductModel->getActiveProducts()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\ProductModel.php:87", "qid": "e88e080301b447646b0d5e2c0231e6b9"}, {"hover": "", "class": "", "duration": "10.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;site_logo&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Models\\SettingModel.php:61", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:3", "function": "        App\\Models\\SettingModel->getSetting()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\SettingModel.php:61", "qid": "a37ab8487a26c7335644d1b2578bad85"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;site_favicon&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Models\\SettingModel.php:61", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:4", "function": "        App\\Models\\SettingModel->getSetting()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\SettingModel.php:61", "qid": "fe67cc06710c4dde31b8837242639a06"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;google_analytics_enabled&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Models\\SettingModel.php:61", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:18", "function": "        App\\Models\\SettingModel->getSetting()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\SettingModel.php:61", "qid": "9e444d225d2fb8c547dd9f4b3fc0015c"}, {"hover": "", "class": "", "duration": "0.15 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;google_analytics_id&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Models\\SettingModel.php:61", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:19", "function": "        App\\Models\\SettingModel->getSetting()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\SettingModel.php:61", "qid": "824601e46199647d694a65d65f6405a6"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\CategoryModel.php:63", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:398", "function": "        App\\Models\\CategoryModel->getActiveCategories()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\CategoryModel.php:63", "qid": "f4b2416fc59b373f2cd4153350460ed2"}, {"hover": "", "class": "", "duration": "7.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `is_active` = 1\n<strong>AND</strong> `show_in_header` = 1\n<strong>ORDER</strong> <strong>BY</strong> `header_order` <strong>ASC</strong>, `title` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PageModel.php:83", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:442", "function": "        App\\Models\\PageModel->getHeaderPages()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\PageModel.php:83", "qid": "eced414133b7ebdc748e1cffecb07d67"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `is_active` = 1\n<strong>AND</strong> `show_in_footer` = 1\n<strong>ORDER</strong> <strong>BY</strong> `footer_order` <strong>ASC</strong>, `title` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PageModel.php:95", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Views\\layouts\\main.php:550", "function": "        App\\Models\\PageModel->getFooterPages()", "index": "  5    "}, {"file": "SYSTEMPATH\\View\\View.php:224", "args": ["D:\\xampp\\htdocs\\nandinihub\\app\\Views\\layouts\\main.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH\\View\\View.php:227", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH\\View\\View.php:240", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "SYSTEMPATH\\Common.php:1173", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Models\\PageModel.php:95", "qid": "4b337379a454429705fe911cf6e3b051"}]}, "badgeValue": 11, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": 1752208352.27101, "duration": "0.029440"}, {"name": "Query", "component": "Database", "start": 1752208352.352026, "duration": "0.015235", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `banners`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": 1752208352.454617, "duration": "0.009786", "query": "<strong>SELECT</strong> `products`.*, `categories`.`name` as `category_name`\n<strong>FROM</strong> `products`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `products`.`category_id`\n<strong>WHERE</strong> `products`.`is_featured` = 1\n<strong>AND</strong> `products`.`is_active` = 1\n<strong>AND</strong> `categories`.`is_active` = 1\n <strong>LIMIT</strong> 8"}, {"name": "Query", "component": "Database", "start": 1752208352.46466, "duration": "0.007750", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": 1752208352.472657, "duration": "0.000428", "query": "<strong>SELECT</strong> `products`.*, `categories`.`name` as `category_name`\n<strong>FROM</strong> `products`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `products`.`category_id`\n<strong>WHERE</strong> `products`.`is_active` = 1\n<strong>AND</strong> `categories`.`is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `products`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 12"}, {"name": "Query", "component": "Database", "start": **********.593743, "duration": "0.010293", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;site_logo&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.604186, "duration": "0.000318", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;site_favicon&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.604599, "duration": "0.000219", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;google_analytics_enabled&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.604877, "duration": "0.000149", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `setting_key` = &#039;google_analytics_id&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.606019, "duration": "0.000265", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `is_active` = 1\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>, `name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.632079, "duration": "0.007280", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `is_active` = 1\n<strong>AND</strong> `show_in_header` = 1\n<strong>ORDER</strong> <strong>BY</strong> `header_order` <strong>ASC</strong>, `title` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.639938, "duration": "0.000215", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `is_active` = 1\n<strong>AND</strong> `show_in_footer` = 1\n<strong>ORDER</strong> <strong>BY</strong> `footer_order` <strong>ASC</strong>, `title` <strong>ASC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layouts/main.php", "component": "Views", "start": **********.344579, "duration": 0.42194604873657227}, {"name": "View: home/index.php", "component": "Views", "start": 1752208352.5888, "duration": 1.177907943725586}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 169 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\Array\\ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Home.php", "name": "Home.php"}, {"path": "APPPATH\\Models\\BannerModel.php", "name": "BannerModel.php"}, {"path": "APPPATH\\Models\\CategoryModel.php", "name": "CategoryModel.php"}, {"path": "APPPATH\\Models\\PageModel.php", "name": "PageModel.php"}, {"path": "APPPATH\\Models\\ProductModel.php", "name": "ProductModel.php"}, {"path": "APPPATH\\Models\\SettingModel.php", "name": "SettingModel.php"}, {"path": "APPPATH\\Views\\home\\index.php", "name": "index.php"}, {"path": "APPPATH\\Views\\layouts\\main.php", "name": "main.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "FCPATH\\vendor\\autoload.php", "name": "autoload.php"}, {"path": "FCPATH\\vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH\\vendor\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH\\vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH\\vendor\\composer\\installed.php", "name": "installed.php"}, {"path": "FCPATH\\vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH\\vendor\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "FCPATH\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH\\vendor\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "FCPATH\\vendor\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 169, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "products", "handler": "\\App\\Controllers\\ProductController::index"}, {"method": "GET", "route": "products/search", "handler": "\\App\\Controllers\\ProductController::search"}, {"method": "GET", "route": "category/([^/]+)", "handler": "\\App\\Controllers\\ProductController::category/$1"}, {"method": "GET", "route": "product/([^/]+)", "handler": "\\App\\Controllers\\ProductController::show/$1"}, {"method": "GET", "route": "cart", "handler": "\\App\\Controllers\\CartController::index"}, {"method": "GET", "route": "cart/clear", "handler": "\\App\\Controllers\\CartController::clear"}, {"method": "GET", "route": "cart/count", "handler": "\\App\\Controllers\\CartController::getCartCount"}, {"method": "GET", "route": "wishlist", "handler": "\\App\\Controllers\\WishlistController::index"}, {"method": "GET", "route": "wishlist/clear", "handler": "\\App\\Controllers\\WishlistController::clear"}, {"method": "GET", "route": "wishlist/count", "handler": "\\App\\Controllers\\WishlistController::getCount"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\AuthController::profile"}, {"method": "GET", "route": "change-password", "handler": "\\App\\Controllers\\AuthController::changePassword"}, {"method": "GET", "route": "orders", "handler": "\\App\\Controllers\\OrderController::index"}, {"method": "GET", "route": "orders/([^/]+)", "handler": "\\App\\Controllers\\OrderController::show/$1"}, {"method": "GET", "route": "checkout", "handler": "\\App\\Controllers\\OrderController::checkout"}, {"method": "GET", "route": "payment/process/([^/]+)", "handler": "\\App\\Controllers\\PaymentController::process/$1"}, {"method": "GET", "route": "payment/callback", "handler": "\\App\\Controllers\\PaymentController::callback"}, {"method": "GET", "route": "payment/success/([^/]+)", "handler": "\\App\\Controllers\\PaymentController::success/$1"}, {"method": "GET", "route": "payment/failure/([^/]+)", "handler": "\\App\\Controllers\\PaymentController::failure/$1"}, {"method": "GET", "route": "coupon/available", "handler": "\\App\\Controllers\\CouponController::getAvailable"}, {"method": "GET", "route": "coupon/check/([^/]+)", "handler": "\\App\\Controllers\\CouponController::check/$1"}, {"method": "GET", "route": "product/([^/]+)/review", "handler": "\\App\\Controllers\\ReviewController::create/$1"}, {"method": "GET", "route": "api/products/([0-9]+)/reviews", "handler": "\\App\\Controllers\\ReviewController::getProductReviews/$1"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\AdminController::index"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\AdminController::index"}, {"method": "GET", "route": "admin/products", "handler": "\\App\\Controllers\\AdminController::products"}, {"method": "GET", "route": "admin/products/create", "handler": "\\App\\Controllers\\AdminController::createProduct"}, {"method": "GET", "route": "admin/products/([0-9]+)/edit", "handler": "\\App\\Controllers\\AdminController::editProduct/$1"}, {"method": "GET", "route": "admin/products/import", "handler": "\\App\\Controllers\\AdminController::importProducts"}, {"method": "GET", "route": "admin/products/import/preview", "handler": "\\App\\Controllers\\AdminController::importPreview"}, {"method": "GET", "route": "admin/products/import/sample", "handler": "\\App\\Controllers\\AdminController::downloadSampleCsv"}, {"method": "GET", "route": "admin/products/export", "handler": "\\App\\Controllers\\AdminController::exportProducts"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\AdminController::categories"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\AdminController::createCategory"}, {"method": "GET", "route": "admin/categories/([0-9]+)/edit", "handler": "\\App\\Controllers\\AdminController::editCategory/$1"}, {"method": "GET", "route": "admin/categories/([0-9]+)/product-count", "handler": "\\App\\Controllers\\AdminController::getCategoryProductCount/$1"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\AdminController::users"}, {"method": "GET", "route": "admin/users/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::viewUser/$1"}, {"method": "GET", "route": "admin/orders", "handler": "\\App\\Controllers\\AdminController::orders"}, {"method": "GET", "route": "admin/orders/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::viewOrder/$1"}, {"method": "GET", "route": "admin/orders/([0-9]+)/print", "handler": "\\App\\Controllers\\AdminController::printOrder/$1"}, {"method": "GET", "route": "admin/orders/([0-9]+)/pdf", "handler": "\\App\\Controllers\\AdminController::downloadOrderPdf/$1"}, {"method": "GET", "route": "admin/reviews", "handler": "\\App\\Controllers\\AdminController::reviews"}, {"method": "GET", "route": "admin/banners", "handler": "\\App\\Controllers\\AdminController::banners"}, {"method": "GET", "route": "admin/banners/create", "handler": "\\App\\Controllers\\AdminController::createBanner"}, {"method": "GET", "route": "admin/banners/([0-9]+)/edit", "handler": "\\App\\Controllers\\AdminController::editBanner/$1"}, {"method": "GET", "route": "admin/coupons", "handler": "\\App\\Controllers\\Admin\\CouponController::index"}, {"method": "GET", "route": "admin/coupons/create", "handler": "\\App\\Controllers\\Admin\\CouponController::create"}, {"method": "GET", "route": "admin/coupons/([0-9]+)/edit", "handler": "\\App\\Controllers\\Admin\\CouponController::edit/$1"}, {"method": "GET", "route": "admin/coupons/([0-9]+)/stats", "handler": "\\App\\Controllers\\Admin\\CouponController::stats/$1"}, {"method": "GET", "route": "admin/shipping", "handler": "\\App\\Controllers\\Admin\\ShippingController::index"}, {"method": "GET", "route": "admin/shipping/create", "handler": "\\App\\Controllers\\Admin\\ShippingController::create"}, {"method": "GET", "route": "admin/shipping/([0-9]+)/edit", "handler": "\\App\\Controllers\\Admin\\ShippingController::edit/$1"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\Admin\\PagesController::index"}, {"method": "GET", "route": "admin/pages/create", "handler": "\\App\\Controllers\\Admin\\PagesController::create"}, {"method": "GET", "route": "admin/pages/([0-9]+)/edit", "handler": "\\App\\Controllers\\Admin\\PagesController::edit/$1"}, {"method": "GET", "route": "admin/settings", "handler": "\\App\\Controllers\\AdminController::settings"}, {"method": "GET", "route": "pages/([^/]+)", "handler": "\\App\\Controllers\\PagesController::show/$1"}, {"method": "GET", "route": "api/v1/products", "handler": "\\App\\Controllers\\Api\\ProductApiController::index"}, {"method": "GET", "route": "api/v1/products/featured", "handler": "\\App\\Controllers\\Api\\ProductApiController::featured"}, {"method": "GET", "route": "api/v1/products/search", "handler": "\\App\\Controllers\\Api\\ProductApiController::search"}, {"method": "GET", "route": "api/v1/products/([^/]+)", "handler": "\\App\\Controllers\\Api\\ProductApiController::show/$1"}, {"method": "GET", "route": "api/v1/products/category/([0-9]+)", "handler": "\\App\\Controllers\\Api\\ProductApiController::byCategory/$1"}, {"method": "GET", "route": "api/v1/categories", "handler": "\\App\\Controllers\\Api\\CategoryApiController::index"}, {"method": "GET", "route": "api/v1/categories/tree", "handler": "\\App\\Controllers\\Api\\CategoryApiController::tree"}, {"method": "GET", "route": "api/v1/categories/popular", "handler": "\\App\\Controllers\\Api\\CategoryApiController::popular"}, {"method": "GET", "route": "api/v1/categories/search", "handler": "\\App\\Controllers\\Api\\CategoryApiController::search"}, {"method": "GET", "route": "api/v1/categories/([^/]+)", "handler": "\\App\\Controllers\\Api\\CategoryApiController::show/$1"}, {"method": "GET", "route": "api/v1/auth/profile", "handler": "\\App\\Controllers\\Api\\AuthApiController::profile"}, {"method": "GET", "route": "api/v1/cart", "handler": "\\App\\Controllers\\Api\\CartApiController::index"}, {"method": "GET", "route": "api/v1/cart/count", "handler": "\\App\\Controllers\\Api\\CartApiController::count"}, {"method": "GET", "route": "api/v1/orders", "handler": "\\App\\Controllers\\Api\\OrderApiController::index"}, {"method": "GET", "route": "api/v1/orders/([0-9]+)", "handler": "\\App\\Controllers\\Api\\OrderApiController::show/$1"}, {"method": "GET", "route": "api/v1/addresses", "handler": "\\App\\Controllers\\Api\\AddressApiController::index"}, {"method": "GET", "route": "api/v1/addresses/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AddressApiController::show/$1"}, {"method": "GET", "route": "api/v1/payment/verify/([^/]+)", "handler": "\\App\\Controllers\\Api\\PaymentApiController::verify/$1"}, {"method": "GET", "route": "api/v1/payment/methods", "handler": "\\App\\Controllers\\Api\\PaymentApiController::methods"}, {"method": "GET", "route": "api/v1/notifications", "handler": "\\App\\Controllers\\Api\\NotificationApiController::getNotifications"}, {"method": "GET", "route": "api/v1/notifications/unread-count", "handler": "\\App\\Controllers\\Api\\NotificationApiController::getUnreadCount"}, {"method": "GET", "route": "api/v1/notifications/preferences", "handler": "\\App\\Controllers\\Api\\NotificationApiController::getPreferences"}, {"method": "GET", "route": "addresses", "handler": "\\App\\Controllers\\UserAddressController::index"}, {"method": "GET", "route": "addresses/add", "handler": "\\App\\Controllers\\UserAddressController::add"}, {"method": "GET", "route": "addresses/edit/([0-9]+)", "handler": "\\App\\Controllers\\UserAddressController::edit/$1"}, {"method": "GET", "route": "addresses/delete/([0-9]+)", "handler": "\\App\\Controllers\\UserAddressController::delete/$1"}, {"method": "POST", "route": "cart/add", "handler": "\\App\\Controllers\\CartController::add"}, {"method": "POST", "route": "cart/update", "handler": "\\App\\Controllers\\CartController::update"}, {"method": "POST", "route": "cart/remove", "handler": "\\App\\Controllers\\CartController::remove"}, {"method": "POST", "route": "cart/clear", "handler": "\\App\\Controllers\\CartController::clear"}, {"method": "POST", "route": "wishlist/add", "handler": "\\App\\Controllers\\WishlistController::add"}, {"method": "POST", "route": "wishlist/remove", "handler": "\\App\\Controllers\\WishlistController::remove"}, {"method": "POST", "route": "wishlist/toggle", "handler": "\\App\\Controllers\\WishlistController::toggle"}, {"method": "POST", "route": "wishlist/clear", "handler": "\\App\\Controllers\\WishlistController::clear"}, {"method": "POST", "route": "wishlist/check", "handler": "\\App\\Controllers\\WishlistController::check"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\AuthController::processLogin"}, {"method": "POST", "route": "register", "handler": "\\App\\Controllers\\AuthController::processRegister"}, {"method": "POST", "route": "profile", "handler": "\\App\\Controllers\\AuthController::updateProfile"}, {"method": "POST", "route": "change-password", "handler": "\\App\\Controllers\\AuthController::updatePassword"}, {"method": "POST", "route": "checkout", "handler": "\\App\\Controllers\\OrderController::processCheckout"}, {"method": "POST", "route": "orders/cancel/([^/]+)", "handler": "\\App\\Controllers\\OrderController::cancelOrder/$1"}, {"method": "POST", "route": "payment/initiate", "handler": "\\App\\Controllers\\PaymentController::initiate"}, {"method": "POST", "route": "payment/callback", "handler": "\\App\\Controllers\\PaymentController::callback"}, {"method": "POST", "route": "payment/webhook", "handler": "\\App\\Controllers\\PaymentController::webhook"}, {"method": "POST", "route": "payment/verify/([^/]+)", "handler": "\\App\\Controllers\\PaymentController::verify/$1"}, {"method": "POST", "route": "coupon/apply", "handler": "\\App\\Controllers\\CouponController::apply"}, {"method": "POST", "route": "coupon/remove", "handler": "\\App\\Controllers\\CouponController::remove"}, {"method": "POST", "route": "coupon/validate", "handler": "\\App\\Controllers\\CouponController::validateCoupon"}, {"method": "POST", "route": "reviews", "handler": "\\App\\Controllers\\ReviewController::store"}, {"method": "POST", "route": "reviews/([0-9]+)/helpful", "handler": "\\App\\Controllers\\ReviewController::helpful/$1"}, {"method": "POST", "route": "admin/products", "handler": "\\App\\Controllers\\AdminController::storeProduct"}, {"method": "POST", "route": "admin/products/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::updateProduct/$1"}, {"method": "POST", "route": "admin/products/import", "handler": "\\App\\Controllers\\AdminController::processImport"}, {"method": "POST", "route": "admin/products/import/execute", "handler": "\\App\\Controllers\\AdminController::executeImport"}, {"method": "POST", "route": "admin/products/([0-9]+)/toggle-status", "handler": "\\App\\Controllers\\AdminController::toggleProductStatus/$1"}, {"method": "POST", "route": "admin/products/([0-9]+)/toggle-featured", "handler": "\\App\\Controllers\\AdminController::toggleProductFeatured/$1"}, {"method": "POST", "route": "admin/products/bulk-action", "handler": "\\App\\Controllers\\AdminController::bulkProductAction"}, {"method": "POST", "route": "admin/categories", "handler": "\\App\\Controllers\\AdminController::storeCategory"}, {"method": "POST", "route": "admin/categories/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::updateCategory/$1"}, {"method": "POST", "route": "admin/categories/([0-9]+)/toggle-status", "handler": "\\App\\Controllers\\AdminController::toggleCategoryStatus/$1"}, {"method": "POST", "route": "admin/users/([0-9]+)/toggle-status", "handler": "\\App\\Controllers\\AdminController::toggleUserStatus/$1"}, {"method": "POST", "route": "admin/orders/([0-9]+)/status", "handler": "\\App\\Controllers\\AdminController::updateOrderStatus/$1"}, {"method": "POST", "route": "admin/orders/([0-9]+)/payment-status", "handler": "\\App\\Controllers\\AdminController::updatePaymentStatus/$1"}, {"method": "POST", "route": "admin/reviews/([0-9]+)/approve", "handler": "\\App\\Controllers\\AdminController::approveReview/$1"}, {"method": "POST", "route": "admin/reviews/([0-9]+)/reject", "handler": "\\App\\Controllers\\AdminController::rejectReview/$1"}, {"method": "POST", "route": "admin/reviews/([0-9]+)/delete", "handler": "\\App\\Controllers\\AdminController::deleteReview/$1"}, {"method": "POST", "route": "admin/banners", "handler": "\\App\\Controllers\\AdminController::storeBanner"}, {"method": "POST", "route": "admin/banners/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::updateBanner/$1"}, {"method": "POST", "route": "admin/banners/([0-9]+)/toggle-status", "handler": "\\App\\Controllers\\AdminController::toggleBannerStatus/$1"}, {"method": "POST", "route": "admin/coupons/store", "handler": "\\App\\Controllers\\Admin\\CouponController::store"}, {"method": "POST", "route": "admin/coupons/([0-9]+)/update", "handler": "\\App\\Controllers\\Admin\\CouponController::update/$1"}, {"method": "POST", "route": "admin/coupons/([0-9]+)/delete", "handler": "\\App\\Controllers\\Admin\\CouponController::delete/$1"}, {"method": "POST", "route": "admin/coupons/([0-9]+)/toggle", "handler": "\\App\\Controllers\\Admin\\CouponController::toggle/$1"}, {"method": "POST", "route": "admin/shipping/store", "handler": "\\App\\Controllers\\Admin\\ShippingController::store"}, {"method": "POST", "route": "admin/shipping/([0-9]+)/update", "handler": "\\App\\Controllers\\Admin\\ShippingController::update/$1"}, {"method": "POST", "route": "admin/shipping/([0-9]+)/delete", "handler": "\\App\\Controllers\\Admin\\ShippingController::delete/$1"}, {"method": "POST", "route": "admin/shipping/([0-9]+)/toggle", "handler": "\\App\\Controllers\\Admin\\ShippingController::toggle/$1"}, {"method": "POST", "route": "admin/shipping/update-sort-order", "handler": "\\App\\Controllers\\Admin\\ShippingController::updateSortOrder"}, {"method": "POST", "route": "admin/pages/store", "handler": "\\App\\Controllers\\Admin\\PagesController::store"}, {"method": "POST", "route": "admin/pages/([0-9]+)/update", "handler": "\\App\\Controllers\\Admin\\PagesController::update/$1"}, {"method": "POST", "route": "admin/pages/([0-9]+)/toggle-status", "handler": "\\App\\Controllers\\Admin\\PagesController::toggleStatus/$1"}, {"method": "POST", "route": "admin/pages/update-header-order", "handler": "\\App\\Controllers\\Admin\\PagesController::updateHeaderOrder"}, {"method": "POST", "route": "admin/pages/update-footer-order", "handler": "\\App\\Controllers\\Admin\\PagesController::updateFooterOrder"}, {"method": "POST", "route": "admin/settings", "handler": "\\App\\Controllers\\AdminController::updateSettings"}, {"method": "POST", "route": "pages/contact/submit", "handler": "\\App\\Controllers\\PagesController::submitContact"}, {"method": "POST", "route": "api/v1/auth/register", "handler": "\\App\\Controllers\\Api\\AuthApiController::register"}, {"method": "POST", "route": "api/v1/auth/login", "handler": "\\App\\Controllers\\Api\\AuthApiController::login"}, {"method": "POST", "route": "api/v1/auth/refresh", "handler": "\\App\\Controllers\\Api\\AuthApiController::refresh"}, {"method": "POST", "route": "api/v1/auth/change-password", "handler": "\\App\\Controllers\\Api\\AuthApiController::changePassword"}, {"method": "POST", "route": "api/v1/auth/logout", "handler": "\\App\\Controllers\\Api\\AuthApiController::logout"}, {"method": "POST", "route": "api/v1/cart/add", "handler": "\\App\\Controllers\\Api\\CartApiController::add"}, {"method": "POST", "route": "api/v1/orders", "handler": "\\App\\Controllers\\Api\\OrderApiController::create"}, {"method": "POST", "route": "api/v1/addresses", "handler": "\\App\\Controllers\\Api\\AddressApiController::create"}, {"method": "POST", "route": "api/v1/payment/initiate", "handler": "\\App\\Controllers\\Api\\PaymentApiController::initiate"}, {"method": "POST", "route": "api/v1/payment/callback", "handler": "\\App\\Controllers\\Api\\PaymentApiController::callback"}, {"method": "POST", "route": "api/v1/notifications/register-token", "handler": "\\App\\Controllers\\Api\\NotificationApiController::registerToken"}, {"method": "POST", "route": "addresses/add", "handler": "\\App\\Controllers\\UserAddressController::add"}, {"method": "POST", "route": "addresses/edit/([0-9]+)", "handler": "\\App\\Controllers\\UserAddressController::edit/$1"}, {"method": "PUT", "route": "api/v1/auth/profile", "handler": "\\App\\Controllers\\Api\\AuthApiController::updateProfile"}, {"method": "PUT", "route": "api/v1/cart/([0-9]+)", "handler": "\\App\\Controllers\\Api\\CartApiController::update/$1"}, {"method": "PUT", "route": "api/v1/orders/([0-9]+)/cancel", "handler": "\\App\\Controllers\\Api\\OrderApiController::cancel/$1"}, {"method": "PUT", "route": "api/v1/addresses/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AddressApiController::update/$1"}, {"method": "PUT", "route": "api/v1/addresses/([0-9]+)/default", "handler": "\\App\\Controllers\\Api\\AddressApiController::setDefault/$1"}, {"method": "PUT", "route": "api/v1/notifications/([0-9]+)/read", "handler": "\\App\\Controllers\\Api\\NotificationApiController::markAsRead/$1"}, {"method": "PUT", "route": "api/v1/notifications/mark-all-read", "handler": "\\App\\Controllers\\Api\\NotificationApiController::markAllAsRead"}, {"method": "PUT", "route": "api/v1/notifications/preferences", "handler": "\\App\\Controllers\\Api\\NotificationApiController::updatePreferences"}, {"method": "DELETE", "route": "admin/products/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::deleteProduct/$1"}, {"method": "DELETE", "route": "admin/categories/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::deleteCategory/$1"}, {"method": "DELETE", "route": "admin/reviews/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::deleteReview/$1"}, {"method": "DELETE", "route": "admin/banners/([0-9]+)", "handler": "\\App\\Controllers\\AdminController::deleteBanner/$1"}, {"method": "DELETE", "route": "admin/pages/([0-9]+)/delete", "handler": "\\App\\Controllers\\Admin\\PagesController::delete/$1"}, {"method": "DELETE", "route": "api/v1/cart/([0-9]+)", "handler": "\\App\\Controllers\\Api\\CartApiController::remove/$1"}, {"method": "DELETE", "route": "api/v1/cart", "handler": "\\App\\Controllers\\Api\\CartApiController::clear"}, {"method": "DELETE", "route": "api/v1/addresses/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AddressApiController::delete/$1"}, {"method": "DELETE", "route": "api/v1/notifications/([0-9]+)", "handler": "\\App\\Controllers\\Api\\NotificationApiController::deleteNotification/$1"}]}, "badgeValue": 89, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "575.96", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.94", "count": 11}}}, "badgeValue": 12, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.926923, "duration": 0.5759620666503906}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": 1752208352.367269, "duration": 3.790855407714844e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": 1752208352.464409, "duration": 2.384185791015625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": 1752208352.472417, "duration": 2.6941299438476562e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": 1752208352.473087, "duration": 1.3828277587890625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.604042, "duration": 2.6941299438476562e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.604506, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.60482, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.605027, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.606286, "duration": 0.0007319450378417969}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.639363, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.640156, "duration": 1.5974044799804688e-05}]}], "vars": {"varData": {"View Data": {"title": "Nandini Hub - Premium Puja Samagri Online", "banners": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>subtitle</th><th>description</th><th>image</th><th>button_text</th><th>button_link</th><th>button_text_2</th><th>button_link_2</th><th>background_color</th><th>text_color</th><th>is_active</th><th>sort_order</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (27)\">Premium Puja Samagri Online</td><td title=\"string (146)\">Discover authentic and high-quality puja items including agarbatti, dhoop, UTF-8</td><td title=\"string (86)\">Experience the divine with our carefully curated collection of traditional UTF-8</td><td title=\"string (30)\">banner_1748326098_608ab673.jpg</td><td title=\"string (8)\">Shop Now</td><td title=\"string (9)\">/products</td><td title=\"string (14)\">View Agarbatti</td><td title=\"string (27)\">/category/agarbatti-incense</td><td title=\"string (7)\">#ff6b35</td><td title=\"string (7)\">#ffffff</td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-05-27 05:56:27</td><td title=\"string (19)\">2025-05-27 06:08:18</td></tr><tr><th>1</th><td title=\"string (1)\">3</td><td title=\"string (27)\">Sacred Agarbatti Collection</td><td title=\"string (60)\">Premium incense sticks for your daily prayers and meditation</td><td title=\"string (85)\">Experience the divine with our carefully curated collection of traditional UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (14)\">Shop Agarbatti</td><td title=\"string (27)\">/category/agarbatti-incense</td><td title=\"string (8)\">View All</td><td title=\"string (9)\">/products</td><td title=\"string (7)\">#ff6b35</td><td title=\"string (7)\">#ffffff</td><td title=\"string (1)\">1</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-05-27 06:35:20</td><td title=\"string (19)\">2025-05-27 06:35:20</td></tr><tr><th>2</th><td title=\"string (1)\">4</td><td title=\"string (22)\">Divine Puja Thali Sets</td><td title=\"string (55)\">Complete brass and silver plated thali sets for worship</td><td title=\"string (76)\">Elegant and traditional puja thali sets crafted with precision and devotion.</td><td title=\"null\"><var>null</var></td><td title=\"string (15)\">Shop Thali Sets</td><td title=\"string (32)\">/category/puja-thali-accessories</td><td title=\"string (10)\">Learn More</td><td title=\"string (9)\">/products</td><td title=\"string (7)\">#f7931e</td><td title=\"string (7)\">#ffffff</td><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (19)\">2025-05-27 06:35:20</td><td title=\"string (19)\">2025-05-27 06:35:20</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (27) \"Premium Puja Samagri Online\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>subtitle</dfn> =&gt; <var>string</var> (146) \"Discover authentic and high-quality puja items including agarbatti, dhoop, d...<div class=\"access-path\">$value[0]['subtitle']</div></dt><dd><pre>Discover authentic and high-quality puja items including agarbatti, dhoop, diyas, and all essential spiritual accessories for your divine worship.\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (86) \"Experience the divine with our carefully curated collection of traditional p...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>Experience the divine with our carefully curated collection of traditional puja items.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (30) \"banner_1748326098_608ab673.jpg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text</dfn> =&gt; <var>string</var> (8) \"Shop Now\"<div class=\"access-path\">$value[0]['button_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link</dfn> =&gt; <var>string</var> (9) \"/products\"<div class=\"access-path\">$value[0]['button_link']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text_2</dfn> =&gt; <var>string</var> (14) \"View Agarbatti\"<div class=\"access-path\">$value[0]['button_text_2']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link_2</dfn> =&gt; <var>string</var> (27) \"/category/agarbatti-incense\"<div class=\"access-path\">$value[0]['button_link_2']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>background_color</dfn> =&gt; <var>string</var> (7) \"#ff6b35\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 107, 53, 1)\"></div></div><div class=\"access-path\">$value[0]['background_color']</div></dt><dd><pre><dfn>#FF6B35</dfn>\n<dfn>rgb(255, 107, 53)</dfn>\n<dfn>hsl(16, 100%, 60%)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>text_color</dfn> =&gt; <var>string</var> (7) \"#ffffff\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 255, 255, 1)\"></div></div><div class=\"access-path\">$value[0]['text_color']</div></dt><dd><pre><dfn>white</dfn>\n<dfn>#FFF</dfn>\n<dfn>#FFFFFF</dfn>\n<dfn>rgb(255, 255, 255)</dfn>\n<dfn>hsl(0, 0%, 100%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 05:56:27\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 06:08:18\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (27) \"Sacred Agarbatti Collection\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>subtitle</dfn> =&gt; <var>string</var> (60) \"Premium incense sticks for your daily prayers and meditation\"<div class=\"access-path\">$value[1]['subtitle']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (85) \"Experience the divine with our carefully curated collection of traditional a...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>Experience the divine with our carefully curated collection of traditional agarbatti.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text</dfn> =&gt; <var>string</var> (14) \"Shop Agarbatti\"<div class=\"access-path\">$value[1]['button_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link</dfn> =&gt; <var>string</var> (27) \"/category/agarbatti-incense\"<div class=\"access-path\">$value[1]['button_link']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text_2</dfn> =&gt; <var>string</var> (8) \"View All\"<div class=\"access-path\">$value[1]['button_text_2']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link_2</dfn> =&gt; <var>string</var> (9) \"/products\"<div class=\"access-path\">$value[1]['button_link_2']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>background_color</dfn> =&gt; <var>string</var> (7) \"#ff6b35\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 107, 53, 1)\"></div></div><div class=\"access-path\">$value[1]['background_color']</div></dt><dd><pre><dfn>#FF6B35</dfn>\n<dfn>rgb(255, 107, 53)</dfn>\n<dfn>hsl(16, 100%, 60%)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>text_color</dfn> =&gt; <var>string</var> (7) \"#ffffff\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 255, 255, 1)\"></div></div><div class=\"access-path\">$value[1]['text_color']</div></dt><dd><pre><dfn>white</dfn>\n<dfn>#FFF</dfn>\n<dfn>#FFFFFF</dfn>\n<dfn>rgb(255, 255, 255)</dfn>\n<dfn>hsl(0, 0%, 100%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 06:35:20\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 06:35:20\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (22) \"Divine Puja Thali Sets\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>subtitle</dfn> =&gt; <var>string</var> (55) \"Complete brass and silver plated thali sets for worship\"<div class=\"access-path\">$value[2]['subtitle']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (76) \"Elegant and traditional puja thali sets crafted with precision and devotion.\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text</dfn> =&gt; <var>string</var> (15) \"Shop Thali Sets\"<div class=\"access-path\">$value[2]['button_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link</dfn> =&gt; <var>string</var> (32) \"/category/puja-thali-accessories\"<div class=\"access-path\">$value[2]['button_link']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_text_2</dfn> =&gt; <var>string</var> (10) \"Learn More\"<div class=\"access-path\">$value[2]['button_text_2']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>button_link_2</dfn> =&gt; <var>string</var> (9) \"/products\"<div class=\"access-path\">$value[2]['button_link_2']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>background_color</dfn> =&gt; <var>string</var> (7) \"#f7931e\"<div class=\"kint-color-preview\"><div style=\"background:rgba(247, 147, 30, 1)\"></div></div><div class=\"access-path\">$value[2]['background_color']</div></dt><dd><pre><dfn>#F7931E</dfn>\n<dfn>rgb(247, 147, 30)</dfn>\n<dfn>hsl(32, 93%, 54%)</dfn>\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>text_color</dfn> =&gt; <var>string</var> (7) \"#ffffff\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 255, 255, 1)\"></div></div><div class=\"access-path\">$value[2]['text_color']</div></dt><dd><pre><dfn>white</dfn>\n<dfn>#FFF</dfn>\n<dfn>#FFFFFF</dfn>\n<dfn>rgb(255, 255, 255)</dfn>\n<dfn>hsl(0, 0%, 100%)</dfn>\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 06:35:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 06:35:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "featuredProducts": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>category_id</th><th>name</th><th>slug</th><th>description</th><th>short_description</th><th>price</th><th>sale_price</th><th>sku</th><th>stock_quantity</th><th>weight</th><th>dimensions</th><th>image</th><th>gallery</th><th>is_featured</th><th>is_active</th><th>meta_title</th><th>meta_description</th><th>created_at</th><th>updated_at</th><th>category_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (20)\">Nag Champa Agarbatti</td><td title=\"string (20)\">nag-champa-agarbatti</td><td title=\"string (111)\">Premium quality Nag Champa incense sticks made from natural ingredients. PeUTF-8</td><td title=\"string (51)\">Premium Nag Champa incense sticks for daily worship</td><td title=\"string (5)\">45.00</td><td title=\"string (5)\">40.00</td><td title=\"string (6)\">AGR001</td><td title=\"string (2)\">98</td><td title=\"string (4)\">0.15</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748324961_ae9d9f95.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-07-09 04:31:58</td><td title=\"string (19)\">Agarbatti &amp; Incense</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (20)\">Sandalwood Agarbatti</td><td title=\"string (20)\">sandalwood-agarbatti</td><td title=\"string (111)\">Pure sandalwood incense sticks with authentic fragrance. Ideal for creatingUTF-8</td><td title=\"string (55)\">Pure sandalwood incense sticks with authentic fragrance</td><td title=\"string (5)\">65.00</td><td title=\"string (5)\">60.00</td><td title=\"string (6)\">AGR002</td><td title=\"string (2)\">72</td><td title=\"string (4)\">0.15</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329326_504b3ba4.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:55:35</td><td title=\"string (19)\">Agarbatti &amp; Incense</td></tr><tr><th>2</th><td title=\"string (1)\">4</td><td title=\"string (1)\">2</td><td title=\"string (24)\">Traditional Dhoop Sticks</td><td title=\"string (24)\">traditional-dhoop-sticks</td><td title=\"string (111)\">Handmade traditional dhoop sticks with natural herbs and resins. Creates thUTF-8</td><td title=\"string (52)\">Handmade traditional dhoop sticks with natural herbs</td><td title=\"string (5)\">55.00</td><td title=\"string (5)\">50.00</td><td title=\"string (6)\">DHP001</td><td title=\"string (2)\">73</td><td title=\"string (4)\">0.20</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329434_704a9bfb.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-07-09 04:31:58</td><td title=\"string (16)\">Dhoop &amp; Sambrani</td></tr><tr><th>3</th><td title=\"string (1)\">6</td><td title=\"string (1)\">3</td><td title=\"string (20)\">Brass Puja Thali Set</td><td title=\"string (20)\">brass-puja-thali-set</td><td title=\"string (120)\">Complete brass puja thali set with diya, incense holder, small bowls and deUTF-8</td><td title=\"string (50)\">Complete brass puja thali set with all accessories</td><td title=\"string (6)\">450.00</td><td title=\"string (6)\">399.00</td><td title=\"string (6)\">PTH001</td><td title=\"string (2)\">22</td><td title=\"string (4)\">0.80</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329458_70403af6.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:55:35</td><td title=\"string (24)\">Puja Thali &amp; Accessories</td></tr><tr><th>4</th><td title=\"string (1)\">7</td><td title=\"string (1)\">3</td><td title=\"string (24)\">Silver Plated Puja Thali</td><td title=\"string (24)\">silver-plated-puja-thali</td><td title=\"string (99)\">Elegant silver plated puja thali with intricate designs. Ideal for special UTF-8</td><td title=\"string (55)\">Elegant silver plated puja thali with intricate designs</td><td title=\"string (6)\">850.00</td><td title=\"string (6)\">800.00</td><td title=\"string (6)\">PTH002</td><td title=\"string (2)\">12</td><td title=\"string (4)\">1.20</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329466_8ec00319.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:57:28</td><td title=\"string (24)\">Puja Thali &amp; Accessories</td></tr><tr><th>5</th><td title=\"string (1)\">8</td><td title=\"string (1)\">4</td><td title=\"string (23)\">Clay Diyas (Pack of 12)</td><td title=\"string (18)\">clay-diyas-pack-12</td><td title=\"string (91)\">Traditional handmade clay diyas perfect for Diwali and daily puja. Pack conUTF-8</td><td title=\"string (44)\">Traditional handmade clay diyas - pack of 12</td><td title=\"string (5)\">60.00</td><td title=\"string (5)\">50.00</td><td title=\"string (6)\">DYA001</td><td title=\"string (3)\">199</td><td title=\"string (4)\">0.50</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329474_d0f7fc34.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:20:45</td><td title=\"string (15)\">Diyas &amp; Candles</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Nag Champa Agarbatti\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"nag-champa-agarbatti\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Premium quality Nag Champa incense sticks made from natural ingredients. Per...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>Premium quality Nag Champa incense sticks made from natural ingredients. Perfect for daily puja and meditation.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (51) \"Premium Nag Champa incense sticks for daily worship\"<div class=\"access-path\">$value[0]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"45.00\"<div class=\"access-path\">$value[0]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"40.00\"<div class=\"access-path\">$value[0]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"AGR001\"<div class=\"access-path\">$value[0]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"98\"<div class=\"access-path\">$value[0]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.15\"<div class=\"access-path\">$value[0]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748324961_ae9d9f95.jpg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-09 04:31:58\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Sandalwood Agarbatti\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"sandalwood-agarbatti\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Pure sandalwood incense sticks with authentic fragrance. Ideal for creating ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>Pure sandalwood incense sticks with authentic fragrance. Ideal for creating peaceful atmosphere during prayers.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (55) \"Pure sandalwood incense sticks with authentic fragrance\"<div class=\"access-path\">$value[1]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"65.00\"<div class=\"access-path\">$value[1]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"60.00\"<div class=\"access-path\">$value[1]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"AGR002\"<div class=\"access-path\">$value[1]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"72\"<div class=\"access-path\">$value[1]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.15\"<div class=\"access-path\">$value[1]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329326_504b3ba4.jpg\"<div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:55:35\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (24) \"Traditional Dhoop Sticks\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (24) \"traditional-dhoop-sticks\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Handmade traditional dhoop sticks with natural herbs and resins. Creates thi...<div class=\"access-path\">$value[2]['description']</div></dt><dd><pre>Handmade traditional dhoop sticks with natural herbs and resins. Creates thick aromatic smoke perfect for puja.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (52) \"Handmade traditional dhoop sticks with natural herbs\"<div class=\"access-path\">$value[2]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"55.00\"<div class=\"access-path\">$value[2]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"50.00\"<div class=\"access-path\">$value[2]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"DHP001\"<div class=\"access-path\">$value[2]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"73\"<div class=\"access-path\">$value[2]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.20\"<div class=\"access-path\">$value[2]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329434_704a9bfb.jpg\"<div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-09 04:31:58\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (16) \"Dhoop &amp; Sambrani\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Brass Puja Thali Set\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"brass-puja-thali-set\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (120) \"Complete brass puja thali set with diya, incense holder, small bowls and dec...<div class=\"access-path\">$value[3]['description']</div></dt><dd><pre>Complete brass puja thali set with diya, incense holder, small bowls and decorative elements. Perfect for daily worship.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (50) \"Complete brass puja thali set with all accessories\"<div class=\"access-path\">$value[3]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (6) \"450.00\"<div class=\"access-path\">$value[3]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (6) \"399.00\"<div class=\"access-path\">$value[3]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"PTH001\"<div class=\"access-path\">$value[3]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[3]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.80\"<div class=\"access-path\">$value[3]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329458_70403af6.jpg\"<div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:55:35\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (24) \"Puja Thali &amp; Accessories\"<div class=\"access-path\">$value[3]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[4]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (24) \"Silver Plated Puja Thali\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (24) \"silver-plated-puja-thali\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (99) \"Elegant silver plated puja thali with intricate designs. Ideal for special o...<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>Elegant silver plated puja thali with intricate designs. Ideal for special occasions and festivals.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (55) \"Elegant silver plated puja thali with intricate designs\"<div class=\"access-path\">$value[4]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (6) \"850.00\"<div class=\"access-path\">$value[4]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (6) \"800.00\"<div class=\"access-path\">$value[4]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"PTH002\"<div class=\"access-path\">$value[4]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[4]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"1.20\"<div class=\"access-path\">$value[4]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329466_8ec00319.jpg\"<div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:57:28\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (24) \"Puja Thali &amp; Accessories\"<div class=\"access-path\">$value[4]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[5]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (23) \"Clay Diyas (Pack of 12)\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (18) \"clay-diyas-pack-12\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (91) \"Traditional handmade clay diyas perfect for Diwali and daily puja. Pack cont...<div class=\"access-path\">$value[5]['description']</div></dt><dd><pre>Traditional handmade clay diyas perfect for Diwali and daily puja. Pack contains 12 pieces.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (44) \"Traditional handmade clay diyas - pack of 12\"<div class=\"access-path\">$value[5]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"60.00\"<div class=\"access-path\">$value[5]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"50.00\"<div class=\"access-path\">$value[5]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"DYA001\"<div class=\"access-path\">$value[5]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (3) \"199\"<div class=\"access-path\">$value[5]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.50\"<div class=\"access-path\">$value[5]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329474_d0f7fc34.jpg\"<div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:20:45\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (15) \"Diyas &amp; Candles\"<div class=\"access-path\">$value[5]['category_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "categories": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (8)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (8)</li><li>Contents (8)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>slug</th><th>description</th><th>image</th><th>is_active</th><th>sort_order</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (19)\">A<PERSON><PERSON><PERSON> &amp; Incense</td><td title=\"string (17)\">agarbatti-incense</td><td title=\"string (81)\">Premium quality agarbatti and incense sticks for daily puja and special occUTF-8</td><td title=\"string (32)\">category_1748330800_00748bc9.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:26:40</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (16)\">Dhoop &amp; Sambrani</td><td title=\"string (14)\">dhoop-sambrani</td><td title=\"string (68)\">Traditional dhoop sticks and sambrani for creating sacred atmosphere</td><td title=\"string (32)\">category_1748331092_acfcd907.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:31:32</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (24)\">Puja Thali &amp; Accessories</td><td title=\"string (22)\">puja-thali-accessories</td><td title=\"string (62)\">Complete puja thali sets and essential accessories for worship</td><td title=\"string (32)\">category_1748263473_86dc9ead.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-26 12:44:33</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (15)\">Diyas &amp; Candles</td><td title=\"string (13)\">diyas-candles</td><td title=\"string (65)\">Traditional diyas, candles and oil lamps for lighting during puja</td><td title=\"string (32)\">category_1748331060_42f0fb9d.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">4</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:31:00</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"string (18)\">Flowers &amp; Garlands</td><td title=\"string (16)\">flowers-garlands</td><td title=\"string (57)\">Fresh flowers, artificial garlands and flower decorations</td><td title=\"string (32)\">category_1748331024_ae30823c.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">5</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:30:24</td></tr><tr><th>5</th><td title=\"string (1)\">6</td><td title=\"string (16)\">Puja Oils &amp; Ghee</td><td title=\"string (14)\">puja-oils-ghee</td><td title=\"string (51)\">Pure oils, ghee and other liquid offerings for puja</td><td title=\"string (32)\">category_1748330839_6b93c1ae.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">6</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:27:19</td></tr><tr><th>6</th><td title=\"string (1)\">7</td><td title=\"string (15)\">Idols &amp; Statues</td><td title=\"string (13)\">idols-statues</td><td title=\"string (46)\">Beautiful idols and statues of various deities</td><td title=\"string (32)\">category_1748330996_df7d0f8d.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">7</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:29:56</td></tr><tr><th>7</th><td title=\"string (1)\">8</td><td title=\"string (20)\">Puja Books &amp; Mantras</td><td title=\"string (18)\">puja-books-mantras</td><td title=\"string (60)\">Religious books, mantra collections and spiritual literature</td><td title=\"string (32)\">category_1748330959_78b95c8d.jpg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">8</td><td title=\"string (19)\">2025-05-26 09:56:34</td><td title=\"string (19)\">2025-05-27 07:29:19</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"agarbatti-incense\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (81) \"Premium quality agarbatti and incense sticks for daily puja and special occa...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>Premium quality agarbatti and incense sticks for daily puja and special occasions\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748330800_00748bc9.jpg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:26:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Dhoop &amp; Sambrani\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (14) \"dhoop-sambrani\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (68) \"Traditional dhoop sticks and sambrani for creating sacred atmosphere\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748331092_acfcd907.jpg\"<div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:31:32\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (24) \"Puja Thali &amp; Accessories\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (22) \"puja-thali-accessories\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (62) \"Complete puja thali sets and essential accessories for worship\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748263473_86dc9ead.jpg\"<div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 12:44:33\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Diyas &amp; Candles\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (13) \"diyas-candles\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (65) \"Traditional diyas, candles and oil lamps for lighting during puja\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748331060_42f0fb9d.jpg\"<div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:31:00\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (18) \"Flowers &amp; Garlands\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (16) \"flowers-garlands\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (57) \"Fresh flowers, artificial garlands and flower decorations\"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748331024_ae30823c.jpg\"<div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:30:24\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Puja Oils &amp; Ghee\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (14) \"puja-oils-ghee\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (51) \"Pure oils, ghee and other liquid offerings for puja\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748330839_6b93c1ae.jpg\"<div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[5]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:27:19\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Idols &amp; Statues\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (13) \"idols-statues\"<div class=\"access-path\">$value[6]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (46) \"Beautiful idols and statues of various deities\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748330996_df7d0f8d.jpg\"<div class=\"access-path\">$value[6]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[6]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:29:56\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Puja Books &amp; Mantras\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (18) \"puja-books-mantras\"<div class=\"access-path\">$value[7]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (60) \"Religious books, mantra collections and spiritual literature\"<div class=\"access-path\">$value[7]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (32) \"category_1748330959_78b95c8d.jpg\"<div class=\"access-path\">$value[7]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[7]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:34\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-27 07:29:19\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "latestProducts": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (8)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (8)</li><li>Contents (8)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>category_id</th><th>name</th><th>slug</th><th>description</th><th>short_description</th><th>price</th><th>sale_price</th><th>sku</th><th>stock_quantity</th><th>weight</th><th>dimensions</th><th>image</th><th>gallery</th><th>is_featured</th><th>is_active</th><th>meta_title</th><th>meta_description</th><th>created_at</th><th>updated_at</th><th>category_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (20)\">Nag Champa Agarbatti</td><td title=\"string (20)\">nag-champa-agarbatti</td><td title=\"string (111)\">Premium quality Nag Champa incense sticks made from natural ingredients. PeUTF-8</td><td title=\"string (51)\">Premium Nag Champa incense sticks for daily worship</td><td title=\"string (5)\">45.00</td><td title=\"string (5)\">40.00</td><td title=\"string (6)\">AGR001</td><td title=\"string (2)\">98</td><td title=\"string (4)\">0.15</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748324961_ae9d9f95.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-07-09 04:31:58</td><td title=\"string (19)\">Agarbatti &amp; Incense</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (20)\">Sandalwood Agarbatti</td><td title=\"string (20)\">sandalwood-agarbatti</td><td title=\"string (111)\">Pure sandalwood incense sticks with authentic fragrance. Ideal for creatingUTF-8</td><td title=\"string (55)\">Pure sandalwood incense sticks with authentic fragrance</td><td title=\"string (5)\">65.00</td><td title=\"string (5)\">60.00</td><td title=\"string (6)\">AGR002</td><td title=\"string (2)\">72</td><td title=\"string (4)\">0.15</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329326_504b3ba4.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:55:35</td><td title=\"string (19)\">Agarbatti &amp; Incense</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (1)\">1</td><td title=\"string (15)\">Mogra Agarbatti</td><td title=\"string (15)\">mogra-agarbatti</td><td title=\"string (101)\">Delicate mogra (jasmine) fragrance incense sticks. Perfect for evening prayUTF-8</td><td title=\"string (39)\">Delicate mogra fragrance incense sticks</td><td title=\"string (5)\">35.00</td><td title=\"string (5)\">30.00</td><td title=\"string (6)\">AGR003</td><td title=\"string (3)\">118</td><td title=\"string (4)\">0.15</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329385_017e8d59.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">0</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-26 08:20:40</td><td title=\"string (19)\">Agarbatti &amp; Incense</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">2</td><td title=\"string (24)\">Traditional Dhoop Sticks</td><td title=\"string (24)\">traditional-dhoop-sticks</td><td title=\"string (111)\">Handmade traditional dhoop sticks with natural herbs and resins. Creates thUTF-8</td><td title=\"string (52)\">Handmade traditional dhoop sticks with natural herbs</td><td title=\"string (5)\">55.00</td><td title=\"string (5)\">50.00</td><td title=\"string (6)\">DHP001</td><td title=\"string (2)\">73</td><td title=\"string (4)\">0.20</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329434_704a9bfb.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-07-09 04:31:58</td><td title=\"string (16)\">Dhoop &amp; Sambrani</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"string (1)\">2</td><td title=\"string (13)\">Sambrani Cups</td><td title=\"string (13)\">sambrani-cups</td><td title=\"string (99)\">Ready-to-use sambrani cups made from pure benzoin resin. Just light and enjUTF-8</td><td title=\"string (55)\">Ready-to-use sambrani cups made from pure benzoin resin</td><td title=\"string (5)\">25.00</td><td title=\"string (5)\">22.00</td><td title=\"string (6)\">SMB001</td><td title=\"string (3)\">148</td><td title=\"string (4)\">0.10</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329451_1e5d084c.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">0</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:57:28</td><td title=\"string (16)\">Dhoop &amp; Sambrani</td></tr><tr><th>5</th><td title=\"string (1)\">6</td><td title=\"string (1)\">3</td><td title=\"string (20)\">Brass Puja Thali Set</td><td title=\"string (20)\">brass-puja-thali-set</td><td title=\"string (120)\">Complete brass puja thali set with diya, incense holder, small bowls and deUTF-8</td><td title=\"string (50)\">Complete brass puja thali set with all accessories</td><td title=\"string (6)\">450.00</td><td title=\"string (6)\">399.00</td><td title=\"string (6)\">PTH001</td><td title=\"string (2)\">22</td><td title=\"string (4)\">0.80</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329458_70403af6.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:55:35</td><td title=\"string (24)\">Puja Thali &amp; Accessories</td></tr><tr><th>6</th><td title=\"string (1)\">7</td><td title=\"string (1)\">3</td><td title=\"string (24)\">Silver Plated Puja Thali</td><td title=\"string (24)\">silver-plated-puja-thali</td><td title=\"string (99)\">Elegant silver plated puja thali with intricate designs. Ideal for special UTF-8</td><td title=\"string (55)\">Elegant silver plated puja thali with intricate designs</td><td title=\"string (6)\">850.00</td><td title=\"string (6)\">800.00</td><td title=\"string (6)\">PTH002</td><td title=\"string (2)\">12</td><td title=\"string (4)\">1.20</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329466_8ec00319.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:57:28</td><td title=\"string (24)\">Puja Thali &amp; Accessories</td></tr><tr><th>7</th><td title=\"string (1)\">8</td><td title=\"string (1)\">4</td><td title=\"string (23)\">Clay Diyas (Pack of 12)</td><td title=\"string (18)\">clay-diyas-pack-12</td><td title=\"string (91)\">Traditional handmade clay diyas perfect for Diwali and daily puja. Pack conUTF-8</td><td title=\"string (44)\">Traditional handmade clay diyas - pack of 12</td><td title=\"string (5)\">60.00</td><td title=\"string (5)\">50.00</td><td title=\"string (6)\">DYA001</td><td title=\"string (3)\">199</td><td title=\"string (4)\">0.50</td><td title=\"string (0)\"></td><td title=\"string (31)\">product_1748329474_d0f7fc34.jpg</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (19)\">2025-05-26 09:56:48</td><td title=\"string (19)\">2025-06-29 07:20:45</td><td title=\"string (15)\">Diyas &amp; Candles</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Nag Champa Agarbatti\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"nag-champa-agarbatti\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Premium quality Nag Champa incense sticks made from natural ingredients. Per...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>Premium quality Nag Champa incense sticks made from natural ingredients. Perfect for daily puja and meditation.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (51) \"Premium Nag Champa incense sticks for daily worship\"<div class=\"access-path\">$value[0]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"45.00\"<div class=\"access-path\">$value[0]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"40.00\"<div class=\"access-path\">$value[0]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"AGR001\"<div class=\"access-path\">$value[0]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"98\"<div class=\"access-path\">$value[0]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.15\"<div class=\"access-path\">$value[0]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748324961_ae9d9f95.jpg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-09 04:31:58\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Sandalwood Agarbatti\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"sandalwood-agarbatti\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Pure sandalwood incense sticks with authentic fragrance. Ideal for creating ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>Pure sandalwood incense sticks with authentic fragrance. Ideal for creating peaceful atmosphere during prayers.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (55) \"Pure sandalwood incense sticks with authentic fragrance\"<div class=\"access-path\">$value[1]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"65.00\"<div class=\"access-path\">$value[1]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"60.00\"<div class=\"access-path\">$value[1]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"AGR002\"<div class=\"access-path\">$value[1]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"72\"<div class=\"access-path\">$value[1]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.15\"<div class=\"access-path\">$value[1]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329326_504b3ba4.jpg\"<div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:55:35\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Mogra Agarbatti\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (15) \"mogra-agarbatti\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (101) \"Delicate mogra (jasmine) fragrance incense sticks. Perfect for evening praye...<div class=\"access-path\">$value[2]['description']</div></dt><dd><pre>Delicate mogra (jasmine) fragrance incense sticks. Perfect for evening prayers and special occasions.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (39) \"Delicate mogra fragrance incense sticks\"<div class=\"access-path\">$value[2]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"35.00\"<div class=\"access-path\">$value[2]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"30.00\"<div class=\"access-path\">$value[2]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"AGR003\"<div class=\"access-path\">$value[2]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (3) \"118\"<div class=\"access-path\">$value[2]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.15\"<div class=\"access-path\">$value[2]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329385_017e8d59.jpg\"<div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-26 08:20:40\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (19) \"Agarbatti &amp; Incense\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (24) \"Traditional Dhoop Sticks\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (24) \"traditional-dhoop-sticks\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (111) \"Handmade traditional dhoop sticks with natural herbs and resins. Creates thi...<div class=\"access-path\">$value[3]['description']</div></dt><dd><pre>Handmade traditional dhoop sticks with natural herbs and resins. Creates thick aromatic smoke perfect for puja.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (52) \"Handmade traditional dhoop sticks with natural herbs\"<div class=\"access-path\">$value[3]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"55.00\"<div class=\"access-path\">$value[3]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"50.00\"<div class=\"access-path\">$value[3]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"DHP001\"<div class=\"access-path\">$value[3]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"73\"<div class=\"access-path\">$value[3]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.20\"<div class=\"access-path\">$value[3]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329434_704a9bfb.jpg\"<div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-09 04:31:58\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (16) \"Dhoop &amp; Sambrani\"<div class=\"access-path\">$value[3]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Sambrani Cups\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (13) \"sambrani-cups\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (99) \"Ready-to-use sambrani cups made from pure benzoin resin. Just light and enjo...<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>Ready-to-use sambrani cups made from pure benzoin resin. Just light and enjoy the divine fragrance.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (55) \"Ready-to-use sambrani cups made from pure benzoin resin\"<div class=\"access-path\">$value[4]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"25.00\"<div class=\"access-path\">$value[4]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"22.00\"<div class=\"access-path\">$value[4]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"SMB001\"<div class=\"access-path\">$value[4]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (3) \"148\"<div class=\"access-path\">$value[4]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.10\"<div class=\"access-path\">$value[4]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329451_1e5d084c.jpg\"<div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:57:28\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (16) \"Dhoop &amp; Sambrani\"<div class=\"access-path\">$value[4]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[5]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Brass Puja Thali Set\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (20) \"brass-puja-thali-set\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (120) \"Complete brass puja thali set with diya, incense holder, small bowls and dec...<div class=\"access-path\">$value[5]['description']</div></dt><dd><pre>Complete brass puja thali set with diya, incense holder, small bowls and decorative elements. Perfect for daily worship.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (50) \"Complete brass puja thali set with all accessories\"<div class=\"access-path\">$value[5]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (6) \"450.00\"<div class=\"access-path\">$value[5]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (6) \"399.00\"<div class=\"access-path\">$value[5]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"PTH001\"<div class=\"access-path\">$value[5]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[5]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.80\"<div class=\"access-path\">$value[5]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329458_70403af6.jpg\"<div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:55:35\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (24) \"Puja Thali &amp; Accessories\"<div class=\"access-path\">$value[5]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[6]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (24) \"Silver Plated Puja Thali\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (24) \"silver-plated-puja-thali\"<div class=\"access-path\">$value[6]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (99) \"Elegant silver plated puja thali with intricate designs. Ideal for special o...<div class=\"access-path\">$value[6]['description']</div></dt><dd><pre>Elegant silver plated puja thali with intricate designs. Ideal for special occasions and festivals.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (55) \"Elegant silver plated puja thali with intricate designs\"<div class=\"access-path\">$value[6]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (6) \"850.00\"<div class=\"access-path\">$value[6]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (6) \"800.00\"<div class=\"access-path\">$value[6]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"PTH002\"<div class=\"access-path\">$value[6]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[6]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"1.20\"<div class=\"access-path\">$value[6]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329466_8ec00319.jpg\"<div class=\"access-path\">$value[6]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:57:28\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (24) \"Puja Thali &amp; Accessories\"<div class=\"access-path\">$value[6]['category_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (21)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[7]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (23) \"Clay Diyas (Pack of 12)\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (18) \"clay-diyas-pack-12\"<div class=\"access-path\">$value[7]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>string</var> (91) \"Traditional handmade clay diyas perfect for Diwali and daily puja. Pack cont...<div class=\"access-path\">$value[7]['description']</div></dt><dd><pre>Traditional handmade clay diyas perfect for Diwali and daily puja. Pack contains 12 pieces.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>short_description</dfn> =&gt; <var>string</var> (44) \"Traditional handmade clay diyas - pack of 12\"<div class=\"access-path\">$value[7]['short_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>price</dfn> =&gt; <var>string</var> (5) \"60.00\"<div class=\"access-path\">$value[7]['price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sale_price</dfn> =&gt; <var>string</var> (5) \"50.00\"<div class=\"access-path\">$value[7]['sale_price']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sku</dfn> =&gt; <var>string</var> (6) \"DYA001\"<div class=\"access-path\">$value[7]['sku']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>stock_quantity</dfn> =&gt; <var>string</var> (3) \"199\"<div class=\"access-path\">$value[7]['stock_quantity']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>weight</dfn> =&gt; <var>string</var> (4) \"0.50\"<div class=\"access-path\">$value[7]['weight']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>dimensions</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['dimensions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (31) \"product_1748329474_d0f7fc34.jpg\"<div class=\"access-path\">$value[7]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['is_featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_title</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['meta_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>meta_description</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['meta_description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-26 09:56:48\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-29 07:20:45\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>string</var> (15) \"Diyas &amp; Candles\"<div class=\"access-path\">$value[7]['category_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost/tiffine/index.php/"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/tiffine/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}