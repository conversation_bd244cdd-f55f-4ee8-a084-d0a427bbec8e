export declare const SlideInData: {
    SlideInRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    SlideInLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    SlideInUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    SlideInDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const SlideOutData: {
    SlideOutRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    SlideOutLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    SlideOutUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    SlideOutDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const SlideIn: {
    SlideInRight: {
        style: string;
        duration: number;
    };
    SlideInLeft: {
        style: string;
        duration: number;
    };
    SlideInUp: {
        style: string;
        duration: number;
    };
    SlideInDown: {
        style: string;
        duration: number;
    };
};
export declare const SlideOut: {
    SlideOutRight: {
        style: string;
        duration: number;
    };
    SlideOutLeft: {
        style: string;
        duration: number;
    };
    SlideOutUp: {
        style: string;
        duration: number;
    };
    SlideOutDown: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Slide.web.d.ts.map