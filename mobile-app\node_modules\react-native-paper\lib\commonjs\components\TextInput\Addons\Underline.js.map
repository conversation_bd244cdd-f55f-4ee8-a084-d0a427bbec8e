{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Underline", "parentState", "error", "colors", "activeColor", "underlineColorCustom", "hasActiveOutline", "style", "theme", "themeOverrides", "isV3", "useInternalTheme", "backgroundColor", "focused", "activeScale", "createElement", "Animated", "View", "testID", "styles", "underline", "md3Underline", "transform", "scaleY", "exports", "StyleSheet", "create", "position", "left", "right", "bottom", "height", "zIndex"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Addons/Underline.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,IAAAE,QAAA,GAAAF,OAAA;AAAyD,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAiBlD,MAAMkB,SAAS,GAAGA,CAAC;EACxBC,WAAW;EACXC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXC,oBAAoB;EACpBC,gBAAgB;EAChBC,KAAK;EACLC,KAAK,EAAEC;AACO,CAAC,KAAK;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAG,IAAAC,yBAAgB,EAACF,cAAc,CAAC;EAEjD,IAAIG,eAAe,GAAGX,WAAW,CAACY,OAAO,GACrCT,WAAW,GACXC,oBAAoB;EAExB,IAAIH,KAAK,EAAEU,eAAe,GAAGT,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAED,KAAK;EAE1C,MAAMY,WAAW,GAAGJ,IAAI,GAAG,CAAC,GAAG,CAAC;EAEhC,oBACElC,KAAA,CAAAuC,aAAA,CAACpC,YAAA,CAAAqC,QAAQ,CAACC,IAAI;IACZC,MAAM,EAAC,sBAAsB;IAC7BX,KAAK,EAAE,CACLY,MAAM,CAACC,SAAS,EAChBV,IAAI,IAAIS,MAAM,CAACE,YAAY,EAC3B;MACET,eAAe;MACf;MACAU,SAAS,EAAE,CACT;QACEC,MAAM,EAAE,CAACb,IAAI,GAAGJ,gBAAgB,GAAGL,WAAW,CAACY,OAAO,IAClDC,WAAW,GACX;MACN,CAAC;IAEL,CAAC,EACDP,KAAK;EACL,CACH,CAAC;AAEN,CAAC;AAACiB,OAAA,CAAAxB,SAAA,GAAAA,SAAA;AAEF,MAAMmB,MAAM,GAAGM,uBAAU,CAACC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACDX,YAAY,EAAE;IACZU,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}