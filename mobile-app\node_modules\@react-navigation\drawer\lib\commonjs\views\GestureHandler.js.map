{"version": 3, "names": ["Dummy", "children", "PanGestureHandler", "TapGestureHandler", "GestureHandlerRootView", "View", "GestureState"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": ";;;;;;AAAA;AACA;AAAoC;AAAA;AAMpC,MAAMA,KAAU,GAAG;EAAA,IAAC;IAAEC;EAAwC,CAAC;EAAA,oBAC7D,0CAAGA,QAAQ,CAAI;AAAA,CAChB;AAEM,MAAMC,iBAAiB,GAC5BF,KAAyD;AAAC;AAErD,MAAMG,iBAAiB,GAC5BH,KAAyD;AAAC;AAErD,MAAMI,sBAAsB,GAAGC,iBAAI;AAAC;AAAA,IAEzBC,YAAY;AAAA;AAAA,WAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;AAAA,GAAZA,YAAY,4BAAZA,YAAY"}