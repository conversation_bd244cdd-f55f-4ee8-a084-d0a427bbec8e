{"version": 3, "names": ["initialUpdaterRun", "isSharedValue", "isChromeDebugger", "WorkletEventHandler", "getInlineStyle", "hasInlineStyles", "flattenArray", "has", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_initialPropsMap", "Map", "filterNonAnimatedProps", "component", "inputProps", "props", "key", "value", "styleProp", "style", "styles", "processedStyle", "map", "viewDescriptors", "handle", "_isFirstRender", "set", "initial", "updater", "get", "animatedProp", "animatedProps", "undefined", "Object", "keys", "for<PERSON>ach", "initialValueKey", "workletEventHandler", "eventNames", "length", "eventName", "listeners"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/PropsFilter.tsx"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,iBAAiB,QAAQ,uBAAc;AAGhD,SAASC,aAAa,QAAQ,qBAAkB;AAChD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,mBAAmB,QAAQ,2BAAwB;AAQ5D,SAASC,cAAc,EAAEC,eAAe,QAAQ,wBAAqB;AACrE,SAASC,YAAY,EAAEC,GAAG,QAAQ,YAAS;AAE3C,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA;AAAA;AAGF,OAAO,MAAMC,WAAW,CAAyB;EACvCC,gBAAgB,GAAG,IAAIC,GAAG,CAAkC,CAAC;EAE9DC,sBAAsBA,CAC3BC,SAAyE,EAChD;IACzB,MAAMC,UAAU,GACdD,SAAS,CAACE,KAAsD;IAClE,MAAMA,KAA8B,GAAG,CAAC,CAAC;IAEzC,KAAK,MAAMC,GAAG,IAAIF,UAAU,EAAE;MAC5B,MAAMG,KAAK,GAAGH,UAAU,CAACE,GAAG,CAAC;MAC7B,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnB,MAAME,SAAS,GAAGJ,UAAU,CAACK,KAAK;QAClC,MAAMC,MAAM,GAAGd,YAAY,CAAaY,SAAS,IAAI,EAAE,CAAC;QAExD,MAAMG,cAA4B,GAAGD,MAAM,CAACE,GAAG,CAAEH,KAAK,IAAK;UACzD,IAAIA,KAAK,IAAIA,KAAK,CAACI,eAAe,EAAE;YAClC,MAAMC,MAAM,GAAGL,KAA4B;YAE3C,IAAIN,SAAS,CAACY,cAAc,EAAE;cAC5B,IAAI,CAACf,gBAAgB,CAACgB,GAAG,CAACF,MAAM,EAAE;gBAChC,GAAGA,MAAM,CAACG,OAAO,CAACV,KAAK;gBACvB,GAAGjB,iBAAiB,CAACwB,MAAM,CAACG,OAAO,CAACC,OAAO;cAC7C,CAAe,CAAC;YAClB;YAEA,OAAO,IAAI,CAAClB,gBAAgB,CAACmB,GAAG,CAACL,MAAM,CAAC,IAAI,CAAC,CAAC;UAChD,CAAC,MAAM,IAAInB,eAAe,CAACc,KAAK,CAAC,EAAE;YACjC,OAAOf,cAAc,CAACe,KAAK,EAAEN,SAAS,CAACY,cAAc,CAAC;UACxD,CAAC,MAAM;YACL,OAAON,KAAK;UACd;QACF,CAAC,CAAC;QACF;QACA;QACAJ,KAAK,CAACC,GAAG,CAAC,GAAGK,cAAc;MAC7B,CAAC,MAAM,IAAIL,GAAG,KAAK,eAAe,EAAE;QAClC,MAAMc,YAAY,GAAGhB,UAAU,CAACiB,aAE/B;QACD,IAAID,YAAY,CAACH,OAAO,KAAKK,SAAS,EAAE;UACtCC,MAAM,CAACC,IAAI,CAACJ,YAAY,CAACH,OAAO,CAACV,KAAK,CAAC,CAACkB,OAAO,CAAEC,eAAe,IAAK;YACnErB,KAAK,CAACqB,eAAe,CAAC,GACpBN,YAAY,CAACH,OAAO,EAAEV,KAAK,CAACmB,eAAe,CAAC;UAChD,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IACL7B,GAAG,CAAC,qBAAqB,EAAEU,KAAK,CAAC,IACjCA,KAAK,CAACoB,mBAAmB,YAAYlC,mBAAmB,EACxD;QACA,IAAIc,KAAK,CAACoB,mBAAmB,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;UACnDtB,KAAK,CAACoB,mBAAmB,CAACC,UAAU,CAACH,OAAO,CAAEK,SAAS,IAAK;YAC1DzB,KAAK,CAACyB,SAAS,CAAC,GAAGjC,GAAG,CAAC,WAAW,EAAEU,KAAK,CAACoB,mBAAmB,CAAC,GAExDpB,KAAK,CAACoB,mBAAmB,CAACI,SAAS,CACnCD,SAAS,CAAC,GACZhC,aAAa;UACnB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLO,KAAK,CAACC,GAAG,CAAC,GAAGR,aAAa;QAC5B;MACF,CAAC,MAAM,IAAIP,aAAa,CAACgB,KAAK,CAAC,EAAE;QAC/B,IAAIJ,SAAS,CAACY,cAAc,EAAE;UAC5BV,KAAK,CAACC,GAAG,CAAC,GAAGC,KAAK,CAACA,KAAK;QAC1B;MACF,CAAC,MAAM,IAAID,GAAG,KAAK,6BAA6B,IAAI,CAACd,gBAAgB,CAAC,CAAC,EAAE;QACvEa,KAAK,CAACC,GAAG,CAAC,GAAGC,KAAK;MACpB;IACF;IACA,OAAOF,KAAK;EACd;AACF", "ignoreList": []}