# Assets Directory

This directory contains all the static assets for the Nandini Hub mobile app.

## Required Assets

### App Icons
- `icon.png` - Main app icon (1024x1024)
- `adaptive-icon.png` - Android adaptive icon foreground (1024x1024)
- `favicon.png` - Web favicon (32x32)

### Splash Screen
- `splash.png` - Splash screen image (1242x2436)

### Notifications
- `notification-icon.png` - Notification icon (96x96)
- `notification-sound.wav` - Notification sound

## Placeholder Images

For development purposes, you can use placeholder images from:
- https://via.placeholder.com/
- https://picsum.photos/

## Production Assets

Before deploying to production, replace all placeholder assets with:
- High-quality app icons in various sizes
- Professional splash screen design
- Custom notification sounds
- Brand-consistent imagery

## Image Guidelines

- Use PNG format for icons and images with transparency
- Use JPG format for photos and images without transparency
- Optimize images for mobile devices
- Follow platform-specific guidelines for icon sizes
- Ensure images are accessible and have good contrast
