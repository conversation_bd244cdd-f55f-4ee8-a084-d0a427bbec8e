import*as e from"../../../ui/lit-html/lit-html.js";import*as t from"../../../ui/legacy/legacy.js";import*as i from"../../../core/i18n/i18n.js";import*as r from"../../../ui/components/buttons/buttons.js";import*as o from"../../../ui/components/helpers/helpers.js";import*as s from"../../../ui/components/icon_button/icon_button.js";import*as n from"../../../ui/components/input/input.js";import*as a from"../models/models.js";import*as l from"../../../ui/components/code_highlighter/code_highlighter.js";import*as d from"../../../core/host/host.js";import*as c from"../../../core/platform/platform.js";import*as p from"../../../core/sdk/sdk.js";import*as h from"../../../third_party/codemirror.next/codemirror.next.js";import*as u from"../../../ui/components/text_editor/text_editor.js";import"../../../ui/components/dialogs/dialogs.js";import*as g from"../../../ui/components/menus/menus.js";import*as m from"../extensions/extensions.js";import*as v from"../../../ui/components/panel_feedback/panel_feedback.js";import*as b from"../../../ui/components/panel_introduction_steps/panel_introduction_steps.js";import*as f from"../controllers/controllers.js";import*as w from"../util/util.js";const y=new CSSStyleSheet;y.replaceSync('*{margin:0;padding:0;box-sizing:border-box;font-size:inherit}.control{background:none;border:none;display:flex;flex-direction:column;align-items:center}.control[disabled]{filter:grayscale(100%);cursor:auto}.icon{display:flex;width:40px;height:40px;border-radius:50%;background:var(--color-red);margin-bottom:8px;position:relative;transition:background 200ms;justify-content:center;align-content:center;align-items:center}.icon::before{--override-white:#fff;box-sizing:border-box;content:"";display:block;width:14px;height:14px;border:1px solid var(--override-white);position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background-color:var(--override-white)}.icon.square::before{border-radius:0}.icon.circle::before{border-radius:50%}.icon:hover{background:var(--color-accent-red)}.icon:active{background:var(--color-red)}.control[disabled] .icon:hover{background:var(--color-red)}.label{font-size:12px;line-height:16px;text-align:center;letter-spacing:0.02em;color:var(--color-text-primary)}\n/*# sourceURL=controlButton.css */\n');var x=self&&self.__decorate||function(e,t,i,r){var o,s=arguments.length,n=s<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(n=(s<3?o(n):s>3?o(t,i,n):o(t,i))||n);return s>3&&n&&Object.defineProperty(t,i,n),n};const{html:S,Decorators:$,LitElement:k}=e,{customElement:E,property:T}=$;let R=class extends k{static styles=[y];constructor(){super(),this.label="",this.shape="square",this.disabled=!1}#e=e=>{this.disabled&&(e.stopPropagation(),e.preventDefault())};render(){return S` <button @click="${this.#e}" .disabled="${this.disabled}" class="control"> <div class="icon ${this.shape}"></div> <div class="label">${this.label}</div> </button> `}};x([T()],R.prototype,"label",void 0),x([T()],R.prototype,"shape",void 0),x([T()],R.prototype,"disabled",void 0),R=x([E("devtools-control-button")],R);var C=Object.freeze({__proto__:null,get ControlButton(){return R}});const N=new CSSStyleSheet;N.replaceSync('*{margin:0;padding:0;outline:none;box-sizing:border-box;font-size:inherit}.wrapper{padding:24px;flex:1}h1{font-size:18px;line-height:24px;letter-spacing:0.02em;color:var(--color-text-primary);margin:0;font-weight:normal}.row-label{font-weight:500;font-size:11px;line-height:16px;letter-spacing:0.8px;text-transform:uppercase;color:var(--color-text-secondary);margin-bottom:8px;display:inline-block;margin-top:32px}.footer{display:flex;justify-content:center;border-top:1px solid var(--color-details-hairline);padding:12px;background:var(--color-background)}.controls{display:flex}.error{margin:16px 0 0;padding:8px;background:var(--color-error-background);color:var(--color-error-text)}.row-label .link{position:relative;top:0.25em}.row-label .link:focus-visible{outline:var(--color-input-outline-active) auto 1px}.header-wrapper{display:flex;align-items:baseline;justify-content:space-between}.checkbox-label{display:inline-flex;align-items:center;overflow:hidden;text-overflow:ellipsis;gap:4px;line-height:1.1;padding:4px}.checkbox-container{display:flex;flex-flow:row wrap;gap:10px}input[type="checkbox"]:focus-visible{outline:var(--color-input-outline-active) auto 1px}\n/*# sourceURL=createRecordingView.css */\n');const M={recordingName:"Recording name",startRecording:"Start recording",createRecording:"Create a new recording",recordingNameIsRequired:"Recording name is required",selectorAttribute:"Selector attribute",cancelRecording:"Cancel recording",selectorTypeCSS:"CSS",selectorTypePierce:"Pierce",selectorTypeARIA:"ARIA",selectorTypeText:"Text",selectorTypeXPath:"XPath",selectorTypes:"Selector types to record",includeNecessarySelectors:"You must choose CSS, Pierce, or XPath as one of your options. Only these selectors are guaranteed to be recorded since ARIA and text selectors may not be unique."},I=i.i18n.registerUIStrings("panels/recorder/components/CreateRecordingView.ts",M),B=i.i18n.getLocalizedString.bind(void 0,I);class A extends Event{static eventName="recordingstarted";name;selectorAttribute;selectorTypesToRecord;constructor(e,t,i){super(A.eventName,{}),this.name=e,this.selectorAttribute=i||void 0,this.selectorTypesToRecord=t}}class P extends Event{static eventName="recordingcancelled";constructor(){super(P.eventName)}}class z extends HTMLElement{static litTagName=e.literal`devtools-create-recording-view`;#t=this.attachShadow({mode:"open"});#i="";#r;#o;connectedCallback(){this.#t.adoptedStyleSheets=[N,n.textInputStyles,n.checkboxStyles],this.#s(),this.#t.querySelector("input")?.focus()}set data(e){this.#o=e.recorderSettings,this.#i=this.#o.defaultTitle}#n(e){this.#r&&(this.#r=void 0,this.#s());"Enter"===e.key&&(this.startRecording(),e.stopPropagation(),e.preventDefault())}startRecording(){const e=this.#t.querySelector("#user-flow-name");if(!e)throw new Error("input#user-flow-name not found");if(!this.#o)throw new Error("settings not set");if(!e.value.trim())return this.#r=new Error(B(M.recordingNameIsRequired)),void this.#s();const t=this.#t.querySelectorAll(".selector-type input[type=checkbox]"),i=[];for(const e of t){const t=e,r=t.value;t.checked&&i.push(r)}if(!i.includes(a.Schema.SelectorType.CSS)&&!i.includes(a.Schema.SelectorType.XPath)&&!i.includes(a.Schema.SelectorType.Pierce))return this.#r=new Error(B(M.includeNecessarySelectors)),void this.#s();for(const e of Object.values(a.Schema.SelectorType))this.#o.setSelectorByType(e,i.includes(e));const r=this.#t.querySelector("#selector-attribute").value.trim();this.#o.selectorAttribute=r,this.dispatchEvent(new A(e.value.trim(),i,r))}#a(){this.dispatchEvent(new P)}#l=()=>{this.#t.querySelector("#user-flow-name")?.select()};#s(){const t=new Map([[a.Schema.SelectorType.ARIA,B(M.selectorTypeARIA)],[a.Schema.SelectorType.CSS,B(M.selectorTypeCSS)],[a.Schema.SelectorType.Text,B(M.selectorTypeText)],[a.Schema.SelectorType.XPath,B(M.selectorTypeXPath)],[a.Schema.SelectorType.Pierce,B(M.selectorTypePierce)]]);e.render(e.html` <div class="wrapper"> <div class="header-wrapper"> <h1>${B(M.createRecording)}</h1> <${r.Button.Button.litTagName} title="${B(M.cancelRecording)}" .data="${{variant:"round",size:"SMALL",iconName:"cross"}}" @click="${this.#a}"></${r.Button.Button.litTagName}> </div> <label class="row-label" for="user-flow-name">${B(M.recordingName)}</label> <input value="${this.#i}" @focus="${this.#l}" @keydown="${this.#n}" class="devtools-text-input" id="user-flow-name"> <label class="row-label" for="selector-attribute"> <span>${B(M.selectorAttribute)}</span> <x-link class="link" href="https://g.co/devtools/recorder#selector"> <${s.Icon.Icon.litTagName} .data="${{iconName:"help",color:"var(--icon-default)",width:"16px",height:"16px"}}"> </${s.Icon.Icon.litTagName}> </x-link> </label> <input value="${this.#o?.selectorAttribute}" placeholder="data-testid" @keydown="${this.#n}" class="devtools-text-input" id="selector-attribute"> <label class="row-label"> <span>${B(M.selectorTypes)}</span> <x-link class="link" href="https://g.co/devtools/recorder#selector"> <${s.Icon.Icon.litTagName} .data="${{iconName:"help",color:"var(--icon-default)",width:"16px",height:"16px"}}"></${s.Icon.Icon.litTagName}> </x-link> </label> <div class="checkbox-container"> ${Object.values(a.Schema.SelectorType).map((i=>{const r=this.#o?.getSelectorByType(i);return e.html` <label class="checkbox-label selector-type"> <input @keydown="${this.#n}" .value="${i}" checked="${e.Directives.ifDefined(r||void 0)}" type="checkbox"> ${t.get(i)||i} </label> `}))} </div> ${this.#r&&e.html` <div class="error" role="alert"> ${this.#r.message} </div> `} </div> <div class="footer"> <div class="controls"> <devtools-control-button @click="${this.startRecording}" .label="${B(M.startRecording)}" .shape="${"circle"}" title="${a.Tooltip.getTooltipForActions(B(M.startRecording),"chrome_recorder.start-recording")}"></devtools-control-button> </div> </div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-create-recording-view",z);var O=Object.freeze({__proto__:null,RecordingStartedEvent:A,RecordingCancelledEvent:P,CreateRecordingView:z});const L=new CSSStyleSheet;L.replaceSync(".token-variable{color:var(--color-token-variable)}.token-property{color:var(--color-token-property)}.token-type{color:var(--color-token-type)}.token-variable-special{color:var(--color-token-variable-special)}.token-definition{color:var(--color-token-definition)}.token-builtin{color:var(--color-token-builtin)}.token-number{color:var(--color-token-number)}.token-string{color:var(--color-token-string)}.token-string-special{color:var(--color-token-string-special)}.token-atom{color:var(--color-token-atom)}.token-keyword{color:var(--color-token-keyword)}.token-comment{color:var(--color-token-comment)}.token-meta{color:var(--color-token-meta)}.token-invalid{color:var(--color-error-text)}.token-tag{color:var(--color-token-tag)}.token-attribute{color:var(--color-token-attribute)}.token-attribute-value{color:var(--color-token-attribute-value)}.token-inserted{color:var(--color-token-inserted)}.token-deleted{color:var(--color-token-deleted)}.token-heading{color:var(--color-token-variable-special);font-weight:bold}.token-link{color:var(--color-token-variable-special);text-decoration:underline}.token-strikethrough{text-decoration:strike-through}.token-strong{font-weight:bold}.token-emphasis{font-style:italic}\n/*# sourceURL=codeHighlighter.css */\n");const D=new CSSStyleSheet;D.replaceSync('*{box-sizing:border-box;font-size:inherit;margin:0;padding:0}devtools-editable-content{background:transparent;border:none;color:var(--color-text-primary);cursor:text;display:inline-block;line-height:18px;min-height:18px;min-width:0.5em;outline:none;overflow-wrap:anywhere}devtools-editable-content:hover,\ndevtools-editable-content:focus{box-shadow:0 0 0 1px var(--color-details-hairline);border-radius:2px}devtools-editable-content[placeholder]:empty::before{content:attr(placeholder);color:var(--color-text-primary);opacity:50%}devtools-editable-content[placeholder]:empty:focus::before{content:""}devtools-suggestion-box{position:absolute;visibility:hidden;display:block}devtools-editable-content:focus ~ devtools-suggestion-box{visibility:visible}.suggestions{background-color:var(--color-background);box-shadow:var(--drop-shadow);min-height:1em;min-width:150px;overflow-x:hidden;overflow-y:auto;position:relative;z-index:100}.suggestions > li{padding:1px;border:1px solid transparent;white-space:nowrap;font-family:var(--source-code-font-family);font-size:var(--source-code-font-size);color:var(--color-text-primary)}.suggestions > li:hover{background-color:var(--item-hover-color)}.suggestions > li.selected{background-color:var(--color-primary);color:var(--color-background)}\n/*# sourceURL=recorderInput.css */\n');function F(e,t="Assertion failed!"){if(!e)throw new Error(t)}const j=e=>{for(const t of Reflect.ownKeys(e)){const i=e[t];(i&&"object"==typeof i||"function"==typeof i)&&j(i)}return Object.freeze(e)};class _{value;constructor(e){this.value=e}}class U{value;constructor(e){this.value=e}}const V=(e,t)=>{if(t instanceof U){F(Array.isArray(e),`Expected an array. Got ${typeof e}.`);const i=[...e],r=Object.keys(t.value).sort(((e,t)=>Number(t)-Number(e)));for(const e of r){const r=t.value[Number(e)];void 0===r?i.splice(Number(e),1):r instanceof _?i.splice(Number(e),0,r.value):i[Number(e)]=V(i[e],r)}return Object.freeze(i)}if("object"==typeof t&&!Array.isArray(t)){F(!Array.isArray(e),"Expected an object. Got an array.");const i={...e},r=Object.keys(t);for(const e of r){const r=t[e];void 0===r?delete i[e]:i[e]=V(i[e],r)}return Object.freeze(i)}return t};var q=self&&self.__decorate||function(e,t,i,r){var o,s=arguments.length,n=s<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(n=(s<3?o(n):s>3?o(t,i,n):o(t,i))||n);return s>3&&n&&Object.defineProperty(t,i,n),n};const{html:G,Decorators:K,Directives:H,LitElement:W}=e,{customElement:X,property:Y,state:J}=K,{classMap:Q}=H,Z={hasChanged:(e,t)=>JSON.stringify(e)!==JSON.stringify(t)};let ee=class extends HTMLElement{static get observedAttributes(){return["disabled","placeholder"]}set disabled(e){this.contentEditable=String(!e)}get disabled(){return"true"!==this.contentEditable}set value(e){this.innerText=e,this.#d()}get value(){return this.innerText}set mimeType(e){this.#c=e,this.#d()}get mimeType(){return this.#c}#c="";constructor(){super(),this.contentEditable="true",this.tabIndex=0,this.addEventListener("focus",(()=>{this.innerHTML=this.innerText})),this.addEventListener("blur",this.#d.bind(this))}#d(){this.#c&&l.CodeHighlighter.highlightNode(this,this.#c)}attributeChangedCallback(e,t,i){if("disabled"===e)this.disabled=null!==i}};ee=q([X("devtools-editable-content")],ee);class te extends Event{static eventName="suggest";constructor(e){super(te.eventName),this.suggestion=e}}class ie extends Event{static eventName="suggestioninit";listeners;constructor(e){super(ie.eventName),this.listeners=e}}let re=class extends W{static styles=[D];#p=[];constructor(){super(),this.options=[],this.expression="",this.cursor=0}#h=e=>{if(F(e instanceof KeyboardEvent,"Bound to the wrong event."),this.#p.length>0)switch(e.key){case"ArrowDown":e.stopPropagation(),e.preventDefault(),this.#u(1);break;case"ArrowUp":e.stopPropagation(),e.preventDefault(),this.#u(-1)}if("Enter"===e.key)this.#p[this.cursor]&&this.#g(this.#p[this.cursor]),e.preventDefault()};#u(e){var t,i;this.cursor=(t=this.cursor+e,i=this.#p.length,(t%i+i)%i)}#g(e){this.dispatchEvent(new te(e))}connectedCallback(){super.connectedCallback(),this.dispatchEvent(new ie([["keydown",this.#h]]))}willUpdate(e){e.has("options")&&(this.options=Object.freeze([...this.options].sort())),e.has("expression")&&(this.cursor=0,this.#p=this.options.filter((e=>e.startsWith(this.expression))))}render(){if(0!==this.#p.length)return G`<ul class="suggestions"> ${this.#p.map(((e,t)=>G`<li class="${Q({selected:t===this.cursor})}" @mousedown="${this.#g.bind(this,e)}"> ${e} `))} </ul>`}};q([Y(Z)],re.prototype,"options",void 0),q([Y()],re.prototype,"expression",void 0),q([J()],re.prototype,"cursor",void 0),re=q([X("devtools-suggestion-box")],re);let oe=class extends W{static shadowRootOptions={...W.shadowRootOptions,delegatesFocus:!0};static styles=[D,L];constructor(){super(),this.options=[],this.expression="",this.placeholder="",this.value="",this.disabled=!1,this.mimeType="",this.addEventListener("blur",this.#m)}#v;get#b(){if(this.#v)return this.#v;const e=this.renderRoot.querySelector("devtools-editable-content");if(!e)throw new Error("Attempted to query node before rendering.");return this.#v=e,e}#m=()=>{window.getSelection()?.removeAllRanges(),this.value=this.#b.value,this.expression=this.#b.value};#f=e=>{F(e.target instanceof Node);const t=document.createRange();t.selectNodeContents(e.target);const i=window.getSelection();i.removeAllRanges(),i.addRange(t)};#h=e=>{"Enter"===e.key&&e.preventDefault()};#w=e=>{this.expression=e.target.value};#y=e=>{for(const[t,i]of e.listeners)this.addEventListener(t,i)};#x=e=>{this.#b.value=e.suggestion,setTimeout(this.blur.bind(this),0)};willUpdate(e){e.has("value")&&(this.expression=this.value)}render(){return G`<devtools-editable-content ?disabled="${this.disabled}" .enterKeyHint="${"done"}" .value="${this.value}" .mimeType="${this.mimeType}" @focus="${this.#f}" @input="${this.#w}" @keydown="${this.#h}" autocapitalize="off" inputmode="text" placeholder="${this.placeholder}" spellcheck="false"></devtools-editable-content> <devtools-suggestion-box @suggestioninit="${this.#y}" @suggest="${this.#x}" .options="${this.options}" .expression="${this.expression}"></devtools-suggestion-box>`}};q([Y(Z)],oe.prototype,"options",void 0),q([J()],oe.prototype,"expression",void 0),q([Y()],oe.prototype,"placeholder",void 0),q([Y()],oe.prototype,"value",void 0),q([Y({type:Boolean})],oe.prototype,"disabled",void 0),q([Y()],oe.prototype,"mimeType",void 0),oe=q([X("devtools-recorder-input")],oe);var se=Object.freeze({__proto__:null,get RecorderInput(){return oe}});const ne=new CSSStyleSheet;ne.replaceSync("*{margin:0;padding:0;box-sizing:border-box;font-size:inherit}*:focus,\n*:focus-visible{outline:none}.wrapper{padding:24px}.header{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:10px}h1{font-size:16px;line-height:19px;color:var(--color-text-primary);font-weight:normal}.icon,\n.icon devtools-icon{width:20px;height:20px}.table{margin-top:35px}.title{font-size:13px;color:var(--color-text-primary);margin-left:10px;flex:1;overflow-x:hidden;white-space:nowrap;text-overflow:ellipsis}.row{display:flex;align-items:center;padding-right:5px;height:28px;border-bottom:1px solid var(--color-details-hairline)}.row:focus-within,\n.row:hover{background-color:var(--color-background-elevation-1)}.row:last-child{border-bottom:none}.actions{display:flex;align-items:center}.actions button{border:none;background-color:transparent;width:24px;height:24px;border-radius:50%;--override-background-color:rgba(26 115 232/15%)}.actions button:hover,\n.actions button:focus-visible{background-color:var(--override-background-color)}.actions .divider{width:1px;height:17px;background-color:var(--color-details-hairline);margin:0 6px}\n/*# sourceURL=recordingListView.css */\n");const ae={savedRecordings:"Saved recordings",createRecording:"Create a new recording",playRecording:"Play recording",deleteRecording:"Delete recording",openRecording:"Open recording"},le=i.i18n.registerUIStrings("panels/recorder/components/RecordingListView.ts",ae),de=i.i18n.getLocalizedString.bind(void 0,le);class ce extends Event{static eventName="createrecording";constructor(){super(ce.eventName)}}class pe extends Event{storageName;static eventName="deleterecording";constructor(e){super(pe.eventName),this.storageName=e}}class he extends Event{storageName;static eventName="openrecording";constructor(e){super(he.eventName),this.storageName=e}}class ue extends Event{storageName;static eventName="playrecording";constructor(e){super(ue.eventName),this.storageName=e}}class ge extends HTMLElement{static litTagName=e.literal`devtools-recording-list-view`;#t=this.attachShadow({mode:"open"});#S={recordings:[],replayAllowed:!0};connectedCallback(){this.#t.adoptedStyleSheets=[ne],o.ScheduledRender.scheduleRender(this,this.#s)}set recordings(e){this.#S.recordings=e,o.ScheduledRender.scheduleRender(this,this.#s)}set replayAllowed(e){this.#S.replayAllowed=e,o.ScheduledRender.scheduleRender(this,this.#s)}#$(){this.dispatchEvent(new ce)}#k(e,t){t.stopPropagation(),this.dispatchEvent(new pe(e))}#E(e,t){t.stopPropagation(),this.dispatchEvent(new he(e))}#T(e,t){t.stopPropagation(),this.dispatchEvent(new ue(e))}#n(e,t){"Enter"===t.key&&this.#E(e,t)}#R(e){e.stopPropagation()}#s=()=>{e.render(e.html` <div class="wrapper"> <div class="header"> <h1>${de(ae.savedRecordings)}</h1> <${r.Button.Button.litTagName} .variant="${"primary"}" @click="${this.#$}" title="${a.Tooltip.getTooltipForActions(de(ae.createRecording),"chrome_recorder.create-recording")}"> ${de(ae.createRecording)} </${r.Button.Button.litTagName}> </div> <div class="table"> ${this.#S.recordings.map((t=>e.html` <div role="button" tabindex="0" aria-label="${de(ae.openRecording)}" class="row" @keydown="${this.#n.bind(this,t.storageName)}" @click="${this.#E.bind(this,t.storageName)}"> <div class="icon"> <${s.Icon.Icon.litTagName} .data="${{iconName:"flow",color:"var(--color-primary)"}}"> </${s.Icon.Icon.litTagName}> </div> <div class="title">${t.name}</div> <div class="actions"> ${this.#S.replayAllowed?e.html` <${r.Button.Button.litTagName} title="${de(ae.playRecording)}" .data="${{variant:"round",iconName:"play"}}" @click="${this.#T.bind(this,t.storageName)}" @keydown="${this.#R}"></${r.Button.Button.litTagName}> <div class="divider"></div>`:""} <${r.Button.Button.litTagName} class="delete-recording-button" title="${de(ae.deleteRecording)}" .data="${{variant:"round",iconName:"bin"}}" @click="${this.#k.bind(this,t.storageName)}" @keydown="${this.#R}"></${r.Button.Button.litTagName}> </div> </div> `))} </div> </div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-recording-list-view",ge);var me=Object.freeze({__proto__:null,CreateRecordingEvent:ce,DeleteRecordingEvent:pe,OpenRecordingEvent:he,PlayRecordingEvent:ue,RecordingListView:ge});const ve=new CSSStyleSheet;ve.replaceSync('*{padding:0;margin:0;box-sizing:border-box;font-size:inherit}.wrapper{display:flex;flex-direction:row;flex:1;height:100%}.main{overflow:hidden;display:flex;flex-direction:column;flex:1}.sections{flex:1;min-height:0;overflow:hidden auto;background-color:var(--color-background);z-index:0;position:relative;container:sections/inline-size}.section{display:flex;padding:0 16px;gap:8px;position:relative}.section::after{content:"";border-bottom:1px solid var(--color-details-hairline);position:absolute;left:0;right:0;bottom:0;z-index:-1}.section:last-child{margin-bottom:70px}.section:last-child::after{content:none}.screenshot-wrapper{flex:0 0 80px;padding-top:32px;z-index:2}@container sections (max-width: 400px){.screenshot-wrapper{display:none}}.screenshot{object-fit:cover;object-position:top center;max-width:100%;width:200px;height:auto;border:1px solid var(--color-details-hairline);border-radius:1px}.content{flex:1;min-width:0}.steps{flex:1;position:relative;align-self:flex-start;overflow:visible}.step{position:relative;padding-left:40px;margin:16px 0}.step .action{font-size:13px;line-height:16px;letter-spacing:0.03em}.recording{color:var(--color-primary);font-style:italic;margin-top:8px;margin-bottom:0}.add-assertion-button{margin-top:8px}.details{max-width:240px;display:flex;flex-direction:column;align-items:flex-end}.url{font-size:12px;line-height:16px;letter-spacing:0.03em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color:var(--color-text-secondary);max-width:100%;margin-bottom:16px}.header{align-items:center;border-bottom:1px solid var(--color-details-hairline);display:flex;flex-wrap:wrap;gap:10px;justify-content:space-between;padding:16px}.header-title-wrapper{max-width:100%}.header-title{align-items:center;display:flex;flex:1;max-width:100%}.header-title::before{content:"";min-width:12px;height:12px;display:inline-block;background:var(--color-primary);border-radius:50%;margin-right:7px}#title-input{box-sizing:content-box;font-family:inherit;font-size:18px;line-height:22px;letter-spacing:0.02em;padding:1px 4px;border:1px solid transparent;border-radius:1px;word-break:break-all}#title-input:hover{border-color:var(--input-outline)}#title-input.has-error{border-color:var(--color-input-outline-error)}#title-input.disabled{color:var(--color-input-text-disabled)}.title-input-error-text{margin-top:4px;margin-left:19px;color:var(--color-error-text)}.title-button-bar{padding-left:2px;display:flex}#title-input:focus + .title-button-bar{display:none}.settings-row{padding:16px 28px;border-bottom:1px solid var(--color-details-hairline);display:flex;flex-flow:row wrap;justify-content:space-between}.settings-title{font-size:14px;line-height:24px;letter-spacing:0.03em;color:var(--color-text-primary);display:flex;align-items:center;align-content:center;gap:5px}.settings{margin-top:4px;display:flex;font-size:12px;line-height:20px;letter-spacing:0.03em;color:var(--color-text-secondary)}.settings.expanded{gap:10px}.settings .separator{width:1px;height:20px;background-color:var(--color-details-hairline);margin:0 5px}.actions{display:flex;align-items:center;flex-wrap:wrap;gap:12px}.is-recording .header-title::before{background:var(--color-red)}.footer{display:flex;justify-content:center;border-top:1px solid var(--color-details-hairline);padding:12px;background:var(--color-background);z-index:1}.controls{align-items:center;display:flex;justify-content:center;position:relative;width:100%}.chevron{width:14px;height:14px;transform:rotate(-90deg)}.expanded .chevron{transform:rotate(0)}.editable-setting{display:flex;flex-direction:row;gap:12px;align-items:center}.editable-setting devtools-select-menu{height:32px}.editable-setting .devtools-text-input{width:fit-content}.wrapping-label{display:inline-flex;align-items:center;gap:12px}.text-editor{height:100%;overflow:auto}.section-toolbar{display:flex;padding:3px;justify-content:space-between;gap:3px}.section-toolbar > devtools-select-menu{min-width:50px}.sections .section-toolbar{justify-content:flex-end}.section-toolbar > *{height:24px}devtools-split-view{flex:1 1 0%;min-height:0}[slot="sidebar"]{display:flex;flex-direction:column;overflow:auto;height:100%;width:100%}[slot="sidebar"] .section-toolbar{border-bottom:1px solid var(--color-details-hairline)}.show-code{margin-right:14px;margin-top:8px}devtools-recorder-extension-view{flex:1}\n/*# sourceURL=recordingView.css */\n');const be=new CSSStyleSheet;be.replaceSync("*{margin:0;padding:0;box-sizing:border-box;font-size:inherit}.title-container{max-width:calc(100% - 18px);font-size:13px;line-height:16px;letter-spacing:0.03em;display:flex;flex-direction:row;gap:3px;outline-offset:3px}.action{display:flex;align-items:flex-start}.title{flex:1;min-width:0}.is-start-of-group .title{font-weight:bold}.error-icon{display:none}.breakpoint-icon{visibility:hidden;cursor:pointer;opacity:0%;fill:var(--color-primary);stroke:#1a73e8;transform:translate(-1.92px,-3px)}.circle-icon{fill:var(--color-primary);stroke:var(--color-background);stroke-width:4px;r:5px;cx:8px;cy:8px}.is-start-of-group .circle-icon{r:7px;fill:var(--color-background);stroke:var(--color-primary);stroke-width:2px}.step.is-success .circle-icon{fill:var(--color-primary);stroke:var(--color-primary)}.step.is-current .circle-icon{stroke-dasharray:24 10;animation:rotate 1s linear infinite;fill:var(--color-background);stroke:var(--color-primary);stroke-width:2px}.error{margin:16px 0 0;padding:8px;background:var(--color-error-background);color:var(--color-error-text);position:relative}@keyframes rotate{0%{transform:translate(8px,8px) rotate(0) translate(-8px,-8px)}100%{transform:translate(8px,8px) rotate(360deg) translate(-8px,-8px)}}.step.is-error .circle-icon{fill:var(--color-error-text);stroke:var(--color-error-text)}.step.is-error .error-icon{display:block;transform:translate(4px,4px)}:host-context(.was-successful) .circle-icon{animation:flash-circle 2s}:host-context(.was-successful) .breakpoint-icon{animation:flash-breakpoint-icon 2s}@keyframes flash-circle{25%{fill:var(--override-color-recording-successful-text);stroke:var(--override-color-recording-successful-text)}75%{fill:var(--override-color-recording-successful-text);stroke:var(--override-color-recording-successful-text)}}@keyframes flash-breakpoint-icon{25%{fill:var(--override-color-recording-successful-text);stroke:var(--override-color-recording-successful-text)}75%{fill:var(--override-color-recording-successful-text);stroke:var(--override-color-recording-successful-text)}}.chevron{width:14px;height:14px;transition:200ms;position:absolute;top:18px;left:24px;transform:rotate(-90deg)}.expanded .chevron{transform:rotate(0deg)}.is-start-of-group .chevron{top:34px}.details{display:none;margin-top:8px;position:relative}.expanded .details{display:block}.step-details{overflow:auto}devtools-recorder-step-editor{border:1px solid var(--color-details-hairline);padding:3px 6px 6px;margin-left:-6px;border-radius:3px}devtools-recorder-step-editor:hover{border:1px solid var(--color-primary)}.summary{display:flex;flex-flow:row nowrap}.filler{flex-grow:1}.subtitle{font-weight:normal;color:var(--color-text-secondary);word-break:break-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main-title{word-break:break-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.step-actions{border:none;border-radius:0;--override-select-menu-show-button-border-radius:0;--override-select-menu-show-button-outline:none;--override-select-menu-show-button-padding:0}.step-actions:hover,\n.step-actions:focus-within{background-color:var(--color-button-secondary-background-hovering)}.step-actions:active{background-color:var(--color-background-highlight)}.step.has-breakpoint .circle-icon{visibility:hidden}.step:not(.is-start-of-group).has-breakpoint .breakpoint-icon{visibility:visible;opacity:100%}.step:not(.is-start-of-group):not(.has-breakpoint) .icon:hover .circle-icon{transition:opacity 0.2s;opacity:0%}.step:not(.is-start-of-group):not(.has-breakpoint) .icon:hover .error-icon{visibility:hidden}.step:not(.is-start-of-group):not(.has-breakpoint) .icon:hover .breakpoint-icon{transition:opacity 0.2s;visibility:visible;opacity:50%}\n/*# sourceURL=stepView.css */\n");const fe=new CSSStyleSheet;fe.replaceSync("*{margin:0;padding:0;box-sizing:border-box;font-size:inherit}.timeline-section{position:relative;padding:16px 0 16px 40px;margin-left:8px;--override-color-recording-successful-text:#36a854;--override-color-recording-successful-background:#e6f4ea}.overlay{position:absolute;width:100vw;height:100%;left:calc(-32px - 80px);top:0;z-index:-1;pointer-events:none}@container (max-width: 400px){.overlay{left:-32px}}:hover .overlay{background:var(--color-background-elevation-1)}.is-selected .overlay{background:var(--color-button-secondary-background-hovering)}:host-context(.is-stopped) .overlay{background:var(--color-execution-line-background);outline:1px solid var(--color-execution-line-outline);z-index:4}.is-start-of-group{padding-top:28px}.is-end-of-group{padding-bottom:24px}.icon{position:absolute;left:4px;transform:translateX(-50%);z-index:2}.bar{position:absolute;left:4px;display:block;transform:translateX(-50%);top:18px;height:calc(100% + 8px);z-index:1}.bar .background{fill:var(--color-background-elevation-1)}.bar .line{fill:var(--color-primary)}.is-first-section .bar{top:32px;height:calc(100% - 8px);display:none}.is-first-section:not(.is-last-section) .bar{display:block}.is-last-section .bar .line{display:none}.is-last-section .bar .background{display:none}:host-context(.is-error) .bar .line{fill:var(--color-error-text)}:host-context(.is-error) .bar .background{fill:var(--color-error-background)}:host-context(.was-successful) .bar .background{animation:flash-background 2s}:host-context(.was-successful) .bar .line{animation:flash-line 2s}@keyframes flash-background{25%{fill:var(--override-color-recording-successful-background)}75%{fill:var(--override-color-recording-successful-background)}}@keyframes flash-line{25%{fill:var(--override-color-recording-successful-text)}75%{fill:var(--override-color-recording-successful-text)}}\n/*# sourceURL=timelineSection.css */\n");class we extends HTMLElement{static litTagName=e.literal`devtools-timeline-section`;#C=!1;#N=!1;#M=!1;#I=!1;#B=!1;constructor(){super();this.attachShadow({mode:"open"}).adoptedStyleSheets=[fe]}set data(e){this.#M=e.isFirstSection,this.#I=e.isLastSection,this.#C=e.isEndOfGroup,this.#N=e.isStartOfGroup,this.#B=e.isSelected,this.#s()}connectedCallback(){this.#s()}#s(){const t={"timeline-section":!0,"is-end-of-group":this.#C,"is-start-of-group":this.#N,"is-first-section":this.#M,"is-last-section":this.#I,"is-selected":this.#B};e.render(e.html` <div class="${e.Directives.classMap(t)}"> <div class="overlay"></div> <div class="icon"><slot name="icon"></slot></div> <svg width="24" height="100%" class="bar"> <rect class="line" x="7" y="0" width="2" height="100%"/> </svg> <slot></slot> </div> `,this.shadowRoot,{host:this})}}o.CustomElements.defineComponent("devtools-timeline-section",we);var ye=Object.freeze({__proto__:null,TimelineSection:we});const xe={setViewportClickTitle:"Set viewport",customStepTitle:"Custom step",clickStepTitle:"Click",doubleClickStepTitle:"Double click",hoverStepTitle:"Hover",emulateNetworkConditionsStepTitle:"Emulate network conditions",changeStepTitle:"Change",closeStepTitle:"Close",scrollStepTitle:"Scroll",keyUpStepTitle:"Key up",navigateStepTitle:"Navigate",keyDownStepTitle:"Key down",waitForElementStepTitle:"Wait for element",waitForExpressionStepTitle:"Wait for expression",elementRoleButton:"Button",elementRoleInput:"Input",elementRoleFallback:"Element",addStepBefore:"Add step before",addStepAfter:"Add step after",removeStep:"Remove step",openStepActions:"Open step actions",addBreakpoint:"Add breakpoint",removeBreakpoint:"Remove breakpoint",copyAs:"Copy as",stepManagement:"Manage steps",breakpoints:"Breakpoints"},Se=i.i18n.registerUIStrings("panels/recorder/components/StepView.ts",xe),$e=i.i18n.getLocalizedString.bind(void 0,Se);class ke extends Event{static eventName="captureselectors";data;constructor(e){super(ke.eventName,{bubbles:!0,composed:!0}),this.data=e}}class Ee extends Event{static eventName="stopselectorscapture";constructor(){super(Ee.eventName,{bubbles:!0,composed:!0})}}class Te extends Event{static eventName="copystep";step;constructor(e){super(Te.eventName,{bubbles:!0,composed:!0}),this.step=e}}class Re extends Event{static eventName="stepchanged";currentStep;newStep;constructor(e,t){super(Re.eventName,{bubbles:!0,composed:!0}),this.currentStep=e,this.newStep=t}}class Ce extends Event{static eventName="addstep";position;stepOrSection;constructor(e,t){super(Ce.eventName,{bubbles:!0,composed:!0}),this.stepOrSection=e,this.position=t}}class Ne extends Event{static eventName="removestep";step;constructor(e){super(Ne.eventName,{bubbles:!0,composed:!0}),this.step=e}}class Me extends Event{static eventName="addbreakpoint";index;constructor(e){super(Me.eventName,{bubbles:!0,composed:!0}),this.index=e}}class Ie extends Event{static eventName="removebreakpoint";index;constructor(e){super(Ie.eventName,{bubbles:!0,composed:!0}),this.index=e}}class Be extends HTMLElement{static litTagName=e.literal`devtools-step-view`;#t=this.attachShadow({mode:"open"});#A;#P;#z="default";#r;#O=!1;#C=!1;#N=!1;#L=0;#D=0;#M=!1;#I=!1;#F=!1;#j=!1;#_;#U=!1;#V=!1;#q=new IntersectionObserver((e=>{this.#V=e[0].isIntersecting}));#G=!1;#K=!0;#H;#W;#B=!1;#o;set data(e){const t=this.#z;this.#A=e.step,this.#P=e.section,this.#z=e.state,this.#r=e.error,this.#C=e.isEndOfGroup,this.#N=e.isStartOfGroup,this.#L=e.stepIndex,this.#D=e.sectionIndex,this.#M=e.isFirstSection,this.#I=e.isLastSection,this.#F=e.isRecording,this.#j=e.isPlaying,this.#G=e.hasBreakpoint,this.#K=e.removable,this.#H=e.builtInConverters,this.#W=e.extensionConverters,this.#B=e.isSelected,this.#o=e.recorderSettings,this.#s(),this.#z===t||"current"!==this.#z||this.#V||this.scrollIntoView()}get step(){return this.#A}get section(){return this.#P}connectedCallback(){this.#t.adoptedStyleSheets=[be],this.#q.observe(this),this.#s()}disconnectedCallback(){this.#q.unobserve(this)}#X(){this.#O=!this.#O,this.#s()}#Y(e){const t=e;"Enter"!==t.key&&" "!==t.key||(this.#X(),e.stopPropagation(),e.preventDefault())}#J(){if(this.#P)return this.#P.title?this.#P.title:e.html`<span class="fallback">(No Title)</span>`;if(!this.#A)throw new Error("Missing both step and section");switch(this.#A.type){case a.Schema.StepType.CustomStep:return $e(xe.customStepTitle);case a.Schema.StepType.SetViewport:return $e(xe.setViewportClickTitle);case a.Schema.StepType.Click:return $e(xe.clickStepTitle);case a.Schema.StepType.DoubleClick:return $e(xe.doubleClickStepTitle);case a.Schema.StepType.Hover:return $e(xe.hoverStepTitle);case a.Schema.StepType.EmulateNetworkConditions:return $e(xe.emulateNetworkConditionsStepTitle);case a.Schema.StepType.Change:return $e(xe.changeStepTitle);case a.Schema.StepType.Close:return $e(xe.closeStepTitle);case a.Schema.StepType.Scroll:return $e(xe.scrollStepTitle);case a.Schema.StepType.KeyUp:return $e(xe.keyUpStepTitle);case a.Schema.StepType.KeyDown:return $e(xe.keyDownStepTitle);case a.Schema.StepType.WaitForElement:return $e(xe.waitForElementStepTitle);case a.Schema.StepType.WaitForExpression:return $e(xe.waitForExpressionStepTitle);case a.Schema.StepType.Navigate:return $e(xe.navigateStepTitle)}}#Q(e){switch(e){case"button":return $e(xe.elementRoleButton);case"input":return $e(xe.elementRoleInput);default:return $e(xe.elementRoleFallback)}}#Z(){if(!this.#A||!("selectors"in this.#A))return"";const e=this.#A.selectors.flat().find((e=>e.startsWith("aria/")));if(!e)return"";const t=e.match(/^aria\/(.+?)(\[role="(.+)"\])?$/);return t?`${this.#Q(t[3])} "${t[1]}"`:""}#ee(){return this.#P?this.#P.url:""}#te(e){const t=this.#A||this.#P?.causingStep;if(!t)throw new Error("Expected step.");this.dispatchEvent(new Re(t,e.data))}#ie(e){switch(e.itemValue){case"add-step-before":{const e=this.#A||this.#P;if(!e)throw new Error("Expected step or section.");this.dispatchEvent(new Ce(e,"before"));break}case"add-step-after":{const e=this.#A||this.#P;if(!e)throw new Error("Expected step or section.");this.dispatchEvent(new Ce(e,"after"));break}case"remove-step":{const e=this.#P?.causingStep;if(!this.#A&&!e)throw new Error("Expected step.");this.dispatchEvent(new Ne(this.#A||e));break}case"add-breakpoint":if(!this.#A)throw new Error("Expected step");this.dispatchEvent(new Me(this.#L));break;case"remove-breakpoint":if(!this.#A)throw new Error("Expected step");this.dispatchEvent(new Ie(this.#L));break;default:{const t=e.itemValue;if(!t.startsWith("copy-step-as-"))throw new Error("Unknown step action.");const i=this.#A||this.#P?.causingStep;if(!i)throw new Error("Step not found.");const r=t.substring("copy-step-as-".length);this.#o&&(this.#o.preferredCopyFormat=r),this.dispatchEvent(new Te(structuredClone(i)))}}}#re(e){e.stopPropagation(),e.preventDefault(),this.#U=!this.#U,this.#s()}#oe(){this.#U=!1,this.#s()}#se(){this.#G?this.dispatchEvent(new Ie(this.#L)):this.dispatchEvent(new Me(this.#L)),this.#s()}#ne(){if(!this.#_)throw new Error("Missing actionsMenuButton");return this.#_}#ae=()=>{const e=[];if(this.#j||(this.#A&&e.push({id:"add-step-before",label:$e(xe.addStepBefore),group:"stepManagement",groupTitle:$e(xe.stepManagement)}),e.push({id:"add-step-after",label:$e(xe.addStepAfter),group:"stepManagement",groupTitle:$e(xe.stepManagement)}),this.#K&&e.push({id:"remove-step",group:"stepManagement",groupTitle:$e(xe.stepManagement),label:$e(xe.removeStep)})),this.#A&&!this.#F&&(this.#G?e.push({id:"remove-breakpoint",label:$e(xe.removeBreakpoint),group:"breakPointManagement",groupTitle:$e(xe.breakpoints)}):e.push({id:"add-breakpoint",label:$e(xe.addBreakpoint),group:"breakPointManagement",groupTitle:$e(xe.breakpoints)})),this.#A){for(const t of this.#H||[])e.push({id:"copy-step-as-"+t.getId(),label:t.getFormatName(),group:"copy",groupTitle:$e(xe.copyAs)});for(const t of this.#W||[])e.push({id:"copy-step-as-"+t.getId(),label:t.getFormatName(),group:"copy",groupTitle:$e(xe.copyAs)})}return e};#le(){const t=this.#ae(),i=new Map;for(const e of t){const t=i.get(e.group);t?t.push(e):i.set(e.group,[e])}const s=[];for(const[e,t]of i)s.push({group:e,groupTitle:t[0].groupTitle,actions:t});return e.html` <${r.Button.Button.litTagName} class="step-actions" title="${$e(xe.openStepActions)}" aria-label="${$e(xe.openStepActions)}" @click="${this.#re}" @keydown="${e=>{e.stopPropagation()}}" on-render="${o.Directives.nodeRenderedCallback((e=>{this.#_=e}))}" .data="${{variant:"toolbar",iconName:"dots-vertical",title:$e(xe.openStepActions)}}"></${r.Button.Button.litTagName}> <${g.Menu.Menu.litTagName} @menucloserequest="${this.#oe}" @menuitemselected="${this.#ie}" .origin="${this.#ne.bind(this)}" .showSelectedItem="${!1}" .showConnector="${!1}" .open="${this.#U}"> ${e.Directives.repeat(s,(e=>e.group),(t=>e.html` <${g.Menu.MenuGroup.litTagName} .name="${t.groupTitle}"> ${e.Directives.repeat(t.actions,(e=>e.id),(t=>e.html`<${g.Menu.MenuItem.litTagName} .value="${t.id}"> ${t.label} </${g.Menu.MenuItem.litTagName}> `))} </${g.Menu.MenuGroup.litTagName}> `))} </${g.Menu.Menu.litTagName}> `}#de=e=>{if(2!==e.button)return;const i=new t.ContextMenu.ContextMenu(e),r=this.#ae(),o=r.filter((e=>e.id.startsWith("copy-step-as-"))),s=r.filter((e=>!e.id.startsWith("copy-step-as-")));for(const e of s){i.section(e.group).appendItem(e.label,(()=>{this.#ie(new g.Menu.MenuItemSelectedEvent(e.id))}))}const n=o.find((e=>e.id==="copy-step-as-"+this.#o?.preferredCopyFormat));if(n&&i.section("copy").appendItem(n.label,(()=>{this.#ie(new g.Menu.MenuItemSelectedEvent(n.id))})),o.length){const e=i.section("copy").appendSubMenuItem($e(xe.copyAs));for(const t of o)t!==n&&e.section(t.group).appendItem(t.label,(()=>{this.#ie(new g.Menu.MenuItemSelectedEvent(t.id))}))}i.show()};#s(){if(!this.#A&&!this.#P)return;const t={step:!0,expanded:this.#O,"is-success":"success"===this.#z,"is-current":"current"===this.#z,"is-outstanding":"outstanding"===this.#z,"is-error":"error"===this.#z,"is-stopped":"stopped"===this.#z,"is-start-of-group":this.#N,"is-first-section":this.#M,"has-breakpoint":this.#G},i=Boolean(this.#A),r=this.#J(),o=this.#A?this.#Z():this.#ee();e.render(e.html` <${we.litTagName} .data="${{isFirstSection:this.#M,isLastSection:this.#I,isStartOfGroup:this.#N,isEndOfGroup:this.#C,isSelected:this.#B}}" @contextmenu="${this.#de}" data-step-index="${this.#L}" data-section-index="${this.#D}" class="${e.Directives.classMap(t)}"> <svg slot="icon" width="24" height="24" height="100%" class="icon"> <circle class="circle-icon"/> <g class="error-icon"> <path d="M1.5 1.5L6.5 6.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M1.5 6.5L6.5 1.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </g> <path @click="${this.#se.bind(this)}" class="breakpoint-icon" d="M2.5 5.5H17.7098L21.4241 12L17.7098 18.5H2.5V5.5Z"/> </svg> <div class="summary"> <div class="title-container ${i?"action":""}" @click="${i&&this.#X.bind(this)}" @keydown="${i&&this.#Y.bind(this)}" tabindex="0" aria-role="${i?"button":""}" aria-label="${i?"Show details for step":""}"> ${i?e.html`<${s.Icon.Icon.litTagName} class="chevron" .data="${{iconName:"triangle-down",color:"var(--color-text-primary)"}}"> </${s.Icon.Icon.litTagName}>`:""} <div class="title"> <div class="main-title" title="${r}">${r}</div> <div class="subtitle" title="${o}">${o}</div> </div> </div> <div class="filler"></div> ${this.#le()} </div> <div class="details"> ${this.#A&&e.html`<devtools-recorder-step-editor .step="${this.#A}" .disabled="${this.#j}" @stepedited="${this.#te}"> </devtools-recorder-step-editor>`} ${this.#P?.causingStep&&e.html`<devtools-recorder-step-editor .step="${this.#P.causingStep}" .isTypeEditable="${!1}" .disabled="${this.#j}" @stepedited="${this.#te}"> </devtools-recorder-step-editor>`} </div> ${this.#r&&e.html` <div class="error" role="alert"> ${this.#r.message} </div> `} </${we.litTagName}> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-step-view",Be);var Ae=Object.freeze({__proto__:null,CaptureSelectorsEvent:ke,StopSelectorsCaptureEvent:Ee,CopyStepEvent:Te,StepChanged:Re,AddStep:Ce,RemoveStep:Ne,AddBreakpointEvent:Me,RemoveBreakpointEvent:Ie,StepView:Be});const Pe=new CSSStyleSheet;Pe.replaceSync('.select-button{display:flex;--override-button-no-right-border-radius:1}.select-button devtools-button{position:relative}.select-menu-item-content-with-icon{display:flex;align-items:center}.select-menu-item-content-with-icon::before{content:"";position:relative;left:0;top:0;background-color:var(--color-text-primary);display:inline-block;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;mask-position:center;-webkit-mask-position:center;width:24px;height:24px;margin-right:4px;mask-image:var(--item-mask-icon);-webkit-mask-image:var(--item-mask-icon)}devtools-select-menu{height:var(--override-select-menu-height,24px);border-radius:0 4px 4px 0;box-sizing:border-box;--override-select-menu-show-button-outline:var(--color-button-outline-focus);--override-select-menu-label-with-arrow-padding:0;--override-select-menu-border:none;--override-select-menu-show-button-padding:0 6px 0 0}devtools-select-menu.primary{border:none;border-left:1px solid var(--override-icon-and-text-color);--override-icon-and-text-color:var(--color-background);--override-select-menu-arrow-color:var(--override-icon-and-text-color);--override-divider-color:var(--override-icon-and-text-color);--override-select-menu-background-color:var(--color-primary);--override-select-menu-active-background-color:var(\n      --override-select-menu-background-color\n    )}devtools-select-menu.primary:hover{--override-select-menu-background-color:var(\n      --color-button-primary-background-hovering\n    )}devtools-select-menu[disabled].primary,\ndevtools-select-menu[disabled].primary:hover{--override-icon-and-text-color:var(--color-text-disabled);--override-select-menu-background-color:var(--color-background-elevation-1)}\n/*# sourceURL=selectButton.css */\n');class ze extends Event{value;static eventName="selectbuttonclick";constructor(e){super(ze.eventName,{bubbles:!0,composed:!0}),this.value=e}}class Oe extends HTMLElement{static litTagName=e.literal`devtools-select-button`;#t=this.attachShadow({mode:"open"});#S={disabled:!1,value:"",items:[],groups:[],variant:"primary"};connectedCallback(){this.#t.adoptedStyleSheets=[Pe],o.ScheduledRender.scheduleRender(this,this.#s)}get disabled(){return this.#S.disabled}set disabled(e){this.#S.disabled=e,o.ScheduledRender.scheduleRender(this,this.#s)}get items(){return this.#S.items}set items(e){this.#S.items=e,o.ScheduledRender.scheduleRender(this,this.#s)}set groups(e){this.#S.groups=e,o.ScheduledRender.scheduleRender(this,this.#s)}get value(){return this.#S.value}set value(e){this.#S.value=e,o.ScheduledRender.scheduleRender(this,this.#s)}get variant(){return this.#S.variant}set variant(e){this.#S.variant=e,o.ScheduledRender.scheduleRender(this,this.#s)}set action(e){this.#S.action=e,o.ScheduledRender.scheduleRender(this,this.#s)}#ce(e){e.stopPropagation(),this.dispatchEvent(new ze(this.#S.value))}#pe(e){this.dispatchEvent(new ze(e.itemValue)),o.ScheduledRender.scheduleRender(this,this.#s)}#he(t,i){return e.html` <${g.Menu.MenuItem.litTagName} .value="${t.value}" .selected="${t.value===i.value}"> ${t.label()} </${g.Menu.MenuItem.litTagName}> `}#ue(t,i){return e.html` <${g.Menu.MenuGroup.litTagName} .name="${t.name}"> ${t.items.map((e=>this.#he(e,i)))} </${g.Menu.MenuGroup.litTagName}> `}#ge(e){return this.#S.action?a.Tooltip.getTooltipForActions(e,this.#S.action):""}#s=()=>{const t=Boolean(this.#S.groups.length),i=t?this.#S.groups.flatMap((e=>e.items)):this.#S.items,o=i.find((e=>e.value===this.#S.value))||i[0];if(!o)return;const s={primary:"primary"===this.#S.variant,secondary:"secondary"===this.#S.variant},n="secondary"===this.#S.variant?"secondary":"primary",a=o.buttonLabel?o.buttonLabel():o.label();e.render(e.html` <div class="select-button" title="${this.#ge(a)||e.nothing}"> ${o?e.html` <${r.Button.Button.litTagName} .disabled="${this.#S.disabled}" .variant="${n}" .iconName="${o.buttonIconName}" @click="${this.#ce}"> ${a} </${r.Button.Button.litTagName}>`:""} <${g.SelectMenu.SelectMenu.litTagName} class="${e.Directives.classMap(s)}" @selectmenuselected="${this.#pe}" ?disabled="${this.#S.disabled}" .showArrow="${!0}" .sideButton="${!1}" .showSelectedItem="${!0}" .disabled="${this.#S.disabled}" .buttonTitle="${e.html``}" .position="${"bottom"}" .horizontalAlignment="${"right"}"> ${t?this.#S.groups.map((e=>this.#ue(e,o))):this.#S.items.map((e=>this.#he(e,o)))} </${g.SelectMenu.SelectMenu.litTagName}> </div>`,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-select-button",Oe);var Le=Object.freeze({__proto__:null,SelectButtonClickEvent:ze,SelectButton:Oe});const De={ReplayNormalButtonLabel:"Replay",ReplayNormalItemLabel:"Normal (Default)",ReplaySlowButtonLabel:"Slow replay",ReplaySlowItemLabel:"Slow",ReplayVerySlowButtonLabel:"Very slow replay",ReplayVerySlowItemLabel:"Very slow",ReplayExtremelySlowButtonLabel:"Extremely slow replay",ReplayExtremelySlowItemLabel:"Extremely slow",speedGroup:"Speed",extensionGroup:"Extensions"},Fe=[{value:"normal",buttonIconName:"play",buttonLabel:()=>Ue(De.ReplayNormalButtonLabel),label:()=>Ue(De.ReplayNormalItemLabel)},{value:"slow",buttonIconName:"play",buttonLabel:()=>Ue(De.ReplaySlowButtonLabel),label:()=>Ue(De.ReplaySlowItemLabel)},{value:"very_slow",buttonIconName:"play",buttonLabel:()=>Ue(De.ReplayVerySlowButtonLabel),label:()=>Ue(De.ReplayVerySlowItemLabel)},{value:"extremely_slow",buttonIconName:"play",buttonLabel:()=>Ue(De.ReplayExtremelySlowButtonLabel),label:()=>Ue(De.ReplayExtremelySlowItemLabel)}],je={normal:d.UserMetrics.RecordingReplaySpeed.Normal,slow:d.UserMetrics.RecordingReplaySpeed.Slow,very_slow:d.UserMetrics.RecordingReplaySpeed.VerySlow,extremely_slow:d.UserMetrics.RecordingReplaySpeed.ExtremelySlow},_e=i.i18n.registerUIStrings("panels/recorder/components/ReplayButton.ts",De),Ue=i.i18n.getLocalizedString.bind(void 0,_e);class Ve extends Event{speed;extension;static eventName="startreplay";constructor(e,t){super(Ve.eventName,{bubbles:!0,composed:!0}),this.speed=e,this.extension=t}}class qe extends HTMLElement{static litTagName=e.literal`devtools-replay-button`;#t=this.attachShadow({mode:"open"});#me=this.#s.bind(this);#S={disabled:!1};#ve;#be=[];set data(e){this.#ve=e.settings,this.#be=e.replayExtensions}get disabled(){return this.#S.disabled}set disabled(e){this.#S.disabled=e,o.ScheduledRender.scheduleRender(this,this.#me)}connectedCallback(){o.ScheduledRender.scheduleRender(this,this.#me)}#fe(e){if(e.stopPropagation(),e.value.startsWith("extension")){this.#ve&&(this.#ve.replayExtension=e.value);const t=Number(e.value.substring("extension".length));return this.dispatchEvent(new Ve("normal",this.#be[t])),void o.ScheduledRender.scheduleRender(this,this.#me)}const t=e.value;this.#ve&&(this.#ve.speed=t,this.#ve.replayExtension=""),d.userMetrics.recordingReplaySpeed(je[t]),this.dispatchEvent(new Ve(e.value)),o.ScheduledRender.scheduleRender(this,this.#me)}#s(){const t=[{name:Ue(De.speedGroup),items:Fe}];this.#be.length&&t.push({name:Ue(De.extensionGroup),items:this.#be.map(((e,t)=>({value:"extension"+t,buttonIconName:"play",buttonLabel:()=>e.getName(),label:()=>e.getName()})))}),e.render(e.html` <${Oe.litTagName} @selectbuttonclick="${this.#fe}" .variant="${"primary"}" .showItemDivider="${!1}" .disabled="${this.#S.disabled}" .action="${"chrome_recorder.replay-recording"}" .value="${this.#ve?.replayExtension||this.#ve?.speed}" .groups="${t}"> </${Oe.litTagName}>`,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-replay-button",qe);var Ge=Object.freeze({__proto__:null,StartReplayEvent:Ve,ReplayButton:qe});const Ke=new CSSStyleSheet;Ke.replaceSync('\n  :host {\n    --current-main-area-size: 50%;\n    --resizer-size: 3px;\n    --min-main-area-size: 200px;\n    --min-sidebar-size: 150px;\n    --main-area-size: calc(max(var(--current-main-area-size), var(--min-main-area-size)));\n\n    height: 100%;\n    width: 100%;\n    display: block;\n    overflow: auto;\n  }\n\n  .wrapper {\n    display: flex;\n    flex-direction: row;\n    height: 100%;\n    width: 100%;\n    container: sidebar / size; /* stylelint-disable-line property-no-unknown */\n  }\n\n  .container {\n    --resizer-position: calc(min(var(--main-area-size), calc(100% - var(--min-sidebar-size))));\n    --min-container-size: calc(var(--min-sidebar-size) + var(--min-main-area-size) + var(--resizer-size));\n\n    display: flex;\n    flex-direction: row;\n    height: 100%;\n    width: 100%;\n    position: relative;\n    gap: var(--resizer-size);\n\n    min-width: var(--min-container-size);\n  }\n\n  #resizer {\n    background-color: var(--color-background-elevation-1);\n    position: absolute;\n    user-select: none;\n\n    /* horizontal */\n    width: var(--resizer-size);\n    cursor: col-resize;\n    left: var(--resizer-position);\n    bottom: 0;\n    top: 0;\n  }\n\n  slot {\n    overflow: auto;\n    display: block;\n  }\n\n  slot[name="main"] {\n\n    /* horizontal */\n    width: var(--resizer-position);\n    min-width: var(--min-main-area-size);\n  }\n\n  slot[name="sidebar"] {\n    flex: 1 0 0;\n\n    min-width: var(--min-sidebar-size);\n  }\n\n  @container sidebar (max-width: 600px) and (min-height: 600px) { /* stylelint-disable-line at-rule-no-unknown */\n    .container {\n      flex-direction: column;\n      min-height: var(--min-container-size);\n      min-width: auto;\n    }\n\n    #resizer {\n      width: auto;\n      height: var(--resizer-size);\n      cursor: row-resize;\n      top: var(--resizer-position);\n      left: 0;\n      right: 0;\n    }\n\n    slot[name="main"] {\n      width: auto;\n      min-width: auto;\n      height: var(--resizer-position);\n      min-height: var(--min-main-area-size);\n    }\n\n    slot[name="sidebar"] {\n      min-width: auto;\n      min-height: var(--min-sidebar-size);\n    }\n  }\n');class He extends HTMLElement{static litTagName=e.literal`devtools-split-view`;#t=this.attachShadow({mode:"open"});#we=[0,0];#ye=0;#xe=[0,0];#q;connectedCallback(){this.style.setProperty("--current-main-area-size","60%"),this.#t.adoptedStyleSheets=[Ke],this.#q=new ResizeObserver((e=>this.#Se(e[0].contentRect))),this.#q.observe(this),this.#s()}#Se=e=>{e.width<=600&&e.height>=600?this.#ye=1:this.#ye=0,this.style.setProperty("--current-main-area-size","60%")};#$e=e=>{const t=this.#t.querySelector("slot[name=main]");if(!t)throw new Error("Main slot not found");const i=t.getBoundingClientRect();this.#xe=[i.width,i.height],this.#we=[e.clientX,e.clientY],window.addEventListener("mousemove",this.#ke,!0),window.addEventListener("mouseup",this.#Ee,!0)};#Ee=()=>{window.removeEventListener("mousemove",this.#ke,!0),window.removeEventListener("mouseup",this.#Ee,!0)};#ke=e=>{const t=[e.clientX,e.clientY][this.#ye]-this.#we[this.#ye],i=this.getBoundingClientRect(),r=[i.width,i.height],o=100*(this.#xe[this.#ye]+t)/r[this.#ye];this.style.setProperty("--current-main-area-size",o+"%")};#s=()=>{e.render(e.html` <div class="wrapper"> <div class="container"> <slot name="main"></slot> <div id="resizer" @mousedown="${this.#$e}"></div> <slot name="sidebar"></slot> </div> </div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-split-view",He);var We=Object.freeze({__proto__:null,SplitView:He});const Xe=new CSSStyleSheet;Xe.replaceSync("*{margin:0;padding:0;outline:none;box-sizing:border-box;font-size:inherit}.extension-view{display:flex;flex-direction:column;height:100%}main{flex:1}iframe{border:none;height:100%;width:100%}header{display:flex;padding:3px 8px;justify-content:space-between;border-bottom:1px solid var(--color-details-hairline)}header > div{align-self:center}.icon{display:block;width:16px;height:16px}.title{display:flex;flex-direction:row;gap:6px;color:var(--color-text-secondary);align-items:center;font-weight:500}\n/*# sourceURL=extensionView.css */\n");const Ye={closeView:"Close",extension:"Content provided by a browser extension"},Je=i.i18n.registerUIStrings("panels/recorder/components/ExtensionView.ts",Ye),Qe=i.i18n.getLocalizedString.bind(void 0,Je);class Ze extends Event{static eventName="recorderextensionviewclosed";constructor(){super(Ze.eventName,{bubbles:!0,composed:!0})}}const et=new URL("../images/extension_icon.svg",import.meta.url).toString();class tt extends HTMLElement{static litTagName=e.literal`devtools-recorder-extension-view`;#t=this.attachShadow({mode:"open"});#Te;connectedCallback(){this.#t.adoptedStyleSheets=[Xe],this.#s()}disconnectedCallback(){this.#Te&&m.ExtensionManager.ExtensionManager.instance().getView(this.#Te.id).hide()}set descriptor(e){this.#Te=e,this.#s(),m.ExtensionManager.ExtensionManager.instance().getView(e.id).show()}#Re(){this.dispatchEvent(new Ze)}#s(){if(!this.#Te)return;const t=m.ExtensionManager.ExtensionManager.instance().getView(this.#Te.id).frame();e.render(e.html` <div class="extension-view"> <header> <div class="title"> <${s.Icon.Icon.litTagName} class="icon" title="${Qe(Ye.extension)}" .data="${{iconPath:et,color:"var(--color-text-secondary)"}}"> </${s.Icon.Icon.litTagName}> ${this.#Te.title} </div> <${r.Button.Button.litTagName} title="${Qe(Ye.closeView)}" .data="${{variant:"round",size:"TINY",iconName:"cross"}}" @click="${this.#Re}"></${r.Button.Button.litTagName}> </header> <main> ${t} <main> </main></main></div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-recorder-extension-view",tt);const it={mobile:"Mobile",desktop:"Desktop",latency:"Latency: {value} ms",upload:"Upload: {value}",download:"Download: {value}",editReplaySettings:"Edit replay settings",replaySettings:"Replay settings",default:"Default",environment:"Environment",screenshotForSection:"Screenshot for this section",editTitle:"Edit title",requiredTitleError:"Title is required",recording:"Recording…",endRecording:"End recording",recordingIsBeingStopped:"Stopping recording…",timeout:"Timeout: {value} ms",network:"Network",timeoutLabel:"Timeout",timeoutExplanation:"The timeout setting (in milliseconds) applies to every action when replaying the recording. For example, if a DOM element identified by a CSS selector does not appear on the page within the specified timeout, the replay fails with an error.",cancelReplay:"Cancel replay",showCode:"Show code",hideCode:"Hide code",addAssertion:"Add assertion",performancePanel:"Performance panel"},rt=i.i18n.registerUIStrings("panels/recorder/components/RecordingView.ts",it),ot=i.i18n.getLocalizedString.bind(void 0,rt);class st extends Event{static eventName="recordingfinished";constructor(){super(st.eventName)}}class nt extends Event{static eventName="playrecording";data;constructor(e={targetPanel:"chrome_recorder",speed:"normal"}){super(nt.eventName),this.data=e}}class at extends Event{static eventName="abortreplay";constructor(){super(at.eventName)}}class lt extends Event{static eventName="recordingchanged";data;constructor(e,t){super(lt.eventName),this.data={currentStep:e,newStep:t}}}class dt extends Event{static eventName="addassertion";constructor(){super(dt.eventName)}}class ct extends Event{static eventName="recordingtitlechanged";title;constructor(e){super(ct.eventName,{}),this.title=e}}class pt extends Event{static eventName="networkconditionschanged";data;constructor(e){super(pt.eventName,{composed:!0,bubbles:!0}),this.data=e}}class ht extends Event{static eventName="timeoutchanged";data;constructor(e){super(ht.eventName,{composed:!0,bubbles:!0}),this.data=e}}const ut=[p.NetworkManager.NoThrottlingConditions,p.NetworkManager.OfflineConditions,p.NetworkManager.Slow3GConditions,p.NetworkManager.Fast3GConditions];class gt extends HTMLElement{static litTagName=e.literal`devtools-recording-view`;#t=this.attachShadow({mode:"open"});#Ce={isPlaying:!1,isPausedOnBreakpoint:!1};#Ne=null;#F=!1;#Me=!1;#Ie=!1;#Be;#Ae=[];#Pe;#ze=[];#ve;#o;#Oe;#Le=new Set;#De;#Fe=!1;#je=!0;#H=[];#W=[];#be;#_e=!1;#Ue="";#Ve="";#qe;#Ge;#Ke;#He=this.#We.bind(this);set data(e){this.#F=e.isRecording,this.#Ce=e.replayState,this.#Me=e.recordingTogglingInProgress,this.#Be=e.currentStep,this.#Ne=e.recording,this.#Ae=this.#Ne.steps,this.#ze=e.sections,this.#ve=e.settings,this.#o=e.recorderSettings,this.#Pe=e.currentError,this.#Oe=e.lastReplayResult,this.#je=e.replayAllowed,this.#Ie=!1,this.#Le=e.breakpointIndexes,this.#H=e.builtInConverters,this.#W=e.extensionConverters,this.#be=e.replayExtensions,this.#Ke=e.extensionDescriptor,this.#Ve=this.#o?.preferredCopyFormat??e.builtInConverters[0]?.getId(),this.#Xe(),this.#s()}connectedCallback(){this.#t.adoptedStyleSheets=[ve,n.textInputStyles],document.addEventListener("copy",this.#He),this.#s()}disconnectedCallback(){document.removeEventListener("copy",this.#He)}scrollToBottom(){const e=this.shadowRoot?.querySelector(".sections");e&&(e.scrollTop=e.scrollHeight)}#Ye(){this.dispatchEvent(new dt)}#Je(){this.dispatchEvent(new st)}#Qe(){this.dispatchEvent(new at)}#Ze(e){this.dispatchEvent(new nt({targetPanel:"chrome_recorder",speed:e.speed,extension:e.extension}))}#et(e){if(!this.#Be)return"default";if(e===this.#Be)return this.#Pe?"error":this.#Ce.isPlaying?this.#Ce.isPausedOnBreakpoint?"stopped":"current":"success";const t=this.#Ae.indexOf(this.#Be);if(-1===t)return"default";return this.#Ae.indexOf(e)<t?"success":"outstanding"}#tt(e){const t=this.#Be;if(!t)return"default";const i=this.#ze.find((e=>e.steps.includes(t)));if(!i&&this.#Pe)return"error";if(e===i)return"success";return this.#ze.indexOf(i)>=this.#ze.indexOf(e)?"success":"outstanding"}#it(t,i,r){const o=this.#Ae.indexOf(i);return e.html` <${Be.litTagName} @click="${this.#rt}" @mouseover="${this.#ot}" @copystep="${this.#st}" .data="${{step:i,state:this.#et(i),error:this.#Be===i?this.#Pe:void 0,isFirstSection:!1,isLastSection:r&&this.#Ae[this.#Ae.length-1]===i,isStartOfGroup:!1,isEndOfGroup:t.steps[t.steps.length-1]===i,stepIndex:o,hasBreakpoint:this.#Le.has(o),sectionIndex:-1,isRecording:this.#F,isPlaying:this.#Ce.isPlaying,removable:this.#Ae.length>1,builtInConverters:this.#H,extensionConverters:this.#W,isSelected:this.#De===i,recorderSettings:this.#o}}"></${Be.litTagName}> `}#ot=e=>{const t=e.target,i=t.step||t.section?.causingStep;i&&!this.#De&&this.#nt(i)};#rt(e){e.stopPropagation();const t=e.target,i=t.step||t.section?.causingStep||null;this.#De!==i&&(this.#De=i,this.#s(),i&&this.#nt(i,!0))}#at(){void 0!==this.#De&&(this.#De=void 0,this.#s())}#lt(e){"Enter"===e.key&&(e.preventDefault(),this.#dt(e))}#dt(e){e.stopPropagation(),this.#Fe=!this.#Fe,this.#s()}#ct(e){const t=ut.find((t=>t.i18nTitleKey===e.itemValue));this.dispatchEvent(new pt(t?.i18nTitleKey===p.NetworkManager.NoThrottlingConditions.i18nTitleKey?void 0:t))}#pt(e){const t=e.target;t.checkValidity()?this.dispatchEvent(new ht(Number(t.value))):t.reportValidity()}#ht=e=>{const t=e.target.innerText.trim();if(!t)return this.#Ie=!0,void this.#s();this.dispatchEvent(new ct(t))};#ut=e=>{switch(e.code){case"Escape":case"Enter":e.target.blur(),e.stopPropagation()}};#gt=()=>{const e=this.#t.getElementById("title-input");e.focus();const t=document.createRange();t.selectNodeContents(e),t.collapse(!1);const i=window.getSelection();i?.removeAllRanges(),i?.addRange(t)};#mt=e=>{const t=e.target;t.matches(".wrapping-label")&&t.querySelector("devtools-select-menu")?.click()};async#vt(e){let t=[...this.#H,...this.#W].find((e=>e.getId()===this.#o?.preferredCopyFormat));if(t||(t=this.#H[0]),!t)throw new Error("No default converter found");let i="";e?i=await t.stringifyStep(e):this.#Ne&&([i]=await t.stringify(this.#Ne)),d.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i);const r=e?function(e){switch(e){case"puppeteer":return d.UserMetrics.RecordingCopiedToClipboard.CopiedStepWithPuppeteer;case"json":return d.UserMetrics.RecordingCopiedToClipboard.CopiedStepWithJSON;case"@puppeteer/replay":return d.UserMetrics.RecordingCopiedToClipboard.CopiedStepWithReplay;default:return d.UserMetrics.RecordingCopiedToClipboard.CopiedStepWithExtension}}(t.getId()):function(e){switch(e){case"puppeteer":return d.UserMetrics.RecordingCopiedToClipboard.CopiedRecordingWithPuppeteer;case"json":return d.UserMetrics.RecordingCopiedToClipboard.CopiedRecordingWithJSON;case"@puppeteer/replay":return d.UserMetrics.RecordingCopiedToClipboard.CopiedRecordingWithReplay;default:return d.UserMetrics.RecordingCopiedToClipboard.CopiedRecordingWithExtension}}(t.getId());d.userMetrics.recordingCopiedToClipboard(r)}#st(e){e.stopPropagation(),this.#vt(e.step)}async#We(e){e.target===document.body&&(e.preventDefault(),await this.#vt(this.#De),d.userMetrics.keyboardShortcutFired("chrome_recorder.copy-recording-or-step"))}#bt(){if(!this.#ve)return e.html``;const t=[];this.#ve.viewportSettings&&(t.push(e.html`<div>${this.#ve.viewportSettings.isMobile?ot(it.mobile):ot(it.desktop)}</div>`),t.push(e.html`<div class="separator"></div>`),t.push(e.html`<div>${this.#ve.viewportSettings.width}×${this.#ve.viewportSettings.height} px</div>`));const i=[];if(this.#Fe){const t=this.#ve.networkConditionsSettings?.i18nTitleKey||p.NetworkManager.NoThrottlingConditions.i18nTitleKey,r=ut.find((e=>e.i18nTitleKey===t));let o="";r&&(o=r.title instanceof Function?r.title():r.title),i.push(e.html`<div class="editable-setting"> <label class="wrapping-label" @click="${this.#mt}"> ${ot(it.network)} <${g.SelectMenu.SelectMenu.litTagName} @selectmenuselected="${this.#ct}" .disabled="${!this.#Ae.find((e=>"navigate"===e.type))}" .showDivider="${!0}" .showArrow="${!0}" .sideButton="${!1}" .showSelectedItem="${!0}" .showConnector="${!1}" .position="${"bottom"}" .buttonTitle="${o}"> ${ut.map((i=>e.html`<${g.Menu.MenuItem.litTagName} .value="${i.i18nTitleKey}" .selected="${t===i.i18nTitleKey}"> ${i.title instanceof Function?i.title():i.title} </${g.Menu.MenuItem.litTagName}>`))} </${g.SelectMenu.SelectMenu.litTagName}> </label> </div>`),i.push(e.html`<div class="editable-setting"> <label class="wrapping-label" title="${ot(it.timeoutExplanation)}"> ${ot(it.timeoutLabel)} <input @input="${this.#pt}" required min="${a.SchemaUtils.minTimeout}" max="${a.SchemaUtils.maxTimeout}" value="${this.#ve.timeout||a.RecordingPlayer.defaultTimeout}" class="devtools-text-input" type="number"> </label> </div>`)}else this.#ve.networkConditionsSettings?this.#ve.networkConditionsSettings.title?i.push(e.html`<div>${this.#ve.networkConditionsSettings.title}</div>`):i.push(e.html`<div> ${ot(it.download,{value:c.NumberUtilities.bytesToString(this.#ve.networkConditionsSettings.download)})}, ${ot(it.upload,{value:c.NumberUtilities.bytesToString(this.#ve.networkConditionsSettings.upload)})}, ${ot(it.latency,{value:this.#ve.networkConditionsSettings.latency})} </div>`):i.push(e.html`<div>${p.NetworkManager.NoThrottlingConditions.title instanceof Function?p.NetworkManager.NoThrottlingConditions.title():p.NetworkManager.NoThrottlingConditions.title}</div>`),i.push(e.html`<div class="separator"></div>`),i.push(e.html`<div>${ot(it.timeout,{value:this.#ve.timeout||a.RecordingPlayer.defaultTimeout})}</div>`);const r=!this.#F&&!this.#Ce.isPlaying,o={"settings-title":!0,expanded:this.#Fe},n={expanded:this.#Fe,settings:!0};return e.html` <div class="settings-row"> <div class="settings-container"> <div class="${e.Directives.classMap(o)}" @keydown="${r&&this.#lt}" @click="${r&&this.#dt}" tabindex="0" role="button" aria-label="${ot(it.editReplaySettings)}"> <span>${ot(it.replaySettings)}</span> ${r?e.html`<${s.Icon.Icon.litTagName} class="chevron" .data="${{iconName:"triangle-down",color:"var(--color-text-primary)"}}"> </${s.Icon.Icon.litTagName}>`:""} </div> <div class="${e.Directives.classMap(n)}"> ${i.length?i:e.html`<div>${ot(it.default)}</div>`} </div> </div> <div class="settings-container"> <div class="settings-title">${ot(it.environment)}</div> <div class="settings"> ${t.length?t:e.html`<div>${ot(it.default)}</div>`} </div> </div> </div> `}#ft(){const e=[...this.#H||[],...this.#W||[]].find((e=>e.getId()===this.#Ve));return e||this.#H[0]}#wt(){if(this.#Ke)return e.html` <${tt.litTagName} .descriptor="${this.#Ke}"> </${tt.litTagName}> `;const t=this.#ft()?.getFormatName();return this.#_e?e.html` <${He.litTagName}> <div slot="main"> ${this.#yt()} </div> <div slot="sidebar"> <div class="section-toolbar"> <${g.SelectMenu.SelectMenu.litTagName} @selectmenuselected="${this.#xt}" .showDivider="${!0}" .showArrow="${!0}" .sideButton="${!1}" .showSelectedItem="${!0}" .showConnector="${!1}" .position="${"bottom"}" .buttonTitle="${t}"> ${this.#H.map((t=>e.html`<${g.Menu.MenuItem.litTagName} .value="${t.getId()}" .selected="${this.#Ve===t.getId()}"> ${t.getFormatName()} </${g.Menu.MenuItem.litTagName}>`))} ${this.#W.map((t=>e.html`<${g.Menu.MenuItem.litTagName} .value="${t.getId()}" .selected="${this.#Ve===t.getId()}"> ${t.getFormatName()} </${g.Menu.MenuItem.litTagName}>`))} </${g.SelectMenu.SelectMenu.litTagName}> <${r.Button.Button.litTagName} title="${a.Tooltip.getTooltipForActions(ot(it.hideCode),"chrome_recorder.toggle-code-view")}" .data="${{variant:"round",size:"SMALL",iconName:"cross"}}" @click="${this.showCodeToggle}"></${r.Button.Button.litTagName}> </div> <div class="text-editor"> <${u.TextEditor.TextEditor.litTagName} .state="${this.#qe}"></${u.TextEditor.TextEditor.litTagName}> </div> </div> </${He.litTagName}> `:this.#yt()}#St(t){return t.screenshot?e.html` <img class="screenshot" src="${t.screenshot}" alt="${ot(it.screenshotForSection)}"> `:null}#$t(){return this.#Ce.isPlaying?e.html` <${r.Button.Button.litTagName} @click="${this.#Qe}" .iconName="${"pause"}" .variant="${"secondary"}"> ${ot(it.cancelReplay)} </${r.Button.Button.litTagName}>`:e.html`<${qe.litTagName} .data="${{settings:this.#o,replayExtensions:this.#be}}" .disabled="${this.#Ce.isPlaying}" @startreplay="${this.#Ze}"> </${qe.litTagName}>`}#kt(e){e.stopPropagation(),this.dispatchEvent(new nt({targetPanel:"timeline",speed:"normal"}))}showCodeToggle=()=>{this.#_e=!this.#_e,d.userMetrics.recordingCodeToggled(this.#_e?d.UserMetrics.RecordingCodeToggled.CodeShown:d.UserMetrics.RecordingCodeToggled.CodeHidden),this.#Xe()};#Xe=async()=>{if(!this.#Ne)return;const e=this.#ft();if(!e)return;const[t,i]=await e.stringify(this.#Ne);this.#Ue=t,this.#Ge=i,this.#Ge?.shift();const r=e.getMediaType(),o=r?await l.CodeHighlighter.languageFromMIME(r):null;this.#qe=h.EditorState.create({doc:this.#Ue,extensions:[u.Config.baseConfiguration(this.#Ue),h.EditorState.readOnly.of(!0),h.EditorView.lineWrapping,o||[]]}),this.#s(),this.dispatchEvent(new Event("code-generated"))};#nt=(e,t=!1)=>{if(!this.#Ge)return;const i=this.#Ae.indexOf(e);if(-1===i)return;const r=this.#t.querySelector("devtools-text-editor");if(!r)return;const o=r.editor;if(!o)return;const s=this.#Ge[2*i],n=this.#Ge[2*i+1];let a=r.createSelection({lineNumber:s+n,columnNumber:0},{lineNumber:s,columnNumber:0});const l=r.state.doc.lineAt(a.main.anchor);a=r.createSelection({lineNumber:s+n-1,columnNumber:l.length+1},{lineNumber:s,columnNumber:0}),o.dispatch({selection:a,effects:t?[h.EditorView.scrollIntoView(a.main,{y:"nearest"})]:void 0})};#xt=e=>{this.#Ve=e.itemValue,this.#o&&(this.#o.preferredCopyFormat=e.itemValue),this.#Xe()};#yt(){return e.html` <div class="sections"> ${this.#_e?"":e.html`<div class="section-toolbar"> <${r.Button.Button.litTagName} @click="${this.showCodeToggle}" class="show-code" .data="${{variant:"secondary",title:a.Tooltip.getTooltipForActions(ot(it.showCode),"chrome_recorder.toggle-code-view")}}"> ${ot(it.showCode)} </${r.Button.Button.litTagName}> </div>`} ${this.#ze.map(((t,i)=>e.html` <div class="section"> <div class="screenshot-wrapper"> ${this.#St(t)} </div> <div class="content"> <div class="steps"> <${Be.litTagName} @click="${this.#rt}" @mouseover="${this.#ot}" .data="${{section:t,state:this.#tt(t),isStartOfGroup:!0,isEndOfGroup:0===t.steps.length,isFirstSection:0===i,isLastSection:i===this.#ze.length-1&&0===t.steps.length,isSelected:this.#De===(t.causingStep||null),sectionIndex:i,isRecording:this.#F,isPlaying:this.#Ce.isPlaying,error:"error"===this.#tt(t)?this.#Pe:void 0,hasBreakpoint:!1,removable:this.#Ae.length>1&&t.causingStep}}"> </${Be.litTagName}> ${t.steps.map((e=>this.#it(t,e,i===this.#ze.length-1)))} ${!this.#Me&&this.#F&&i===this.#ze.length-1?e.html`<devtools-button class="step add-assertion-button" .data="${{variant:"secondary",title:ot(it.addAssertion)}}" @click="${this.#Ye}">${ot(it.addAssertion)}</devtools-button>`:void 0} ${this.#F&&i===this.#ze.length-1?e.html`<div class="step recording">${ot(it.recording)}</div>`:null} </div> </div> </div> `))} </div> `}#Et(){if(!this.#Ne)return"";const{title:t}=this.#Ne,i=!this.#Ce.isPlaying&&!this.#F;return e.html` <div class="header"> <div class="header-title-wrapper"> <div class="header-title"> <span @blur="${this.#ht}" @keydown="${this.#ut}" id="title-input" .contentEditable="${i?"true":"false"}" class="${e.Directives.classMap({"has-error":this.#Ie,disabled:!i})}" .innerText="${e.Directives.live(t)}"></span> <div class="title-button-bar"> <${r.Button.Button.litTagName} @click="${this.#gt}" .data="${{disabled:!i,variant:"toolbar",iconName:"edit",title:ot(it.editTitle)}}"></${r.Button.Button.litTagName}> </div> </div> ${this.#Ie?e.html`<div class="title-input-error-text"> ${ot(it.requiredTitleError)} </div>`:""} </div> ${!this.#F&&this.#je?e.html`<div class="actions"> <${r.Button.Button.litTagName} @click="${this.#kt}" .data="${{disabled:this.#Ce.isPlaying,variant:"secondary",iconName:"performance",title:ot(it.performancePanel)}}"> ${ot(it.performancePanel)} </${r.Button.Button.litTagName}> ${this.#$t()} </div>`:""} </div>`}#Tt(){if(!this.#F)return"";const t=this.#Me?ot(it.recordingIsBeingStopped):ot(it.endRecording);return e.html` <div class="footer"> <div class="controls"> <devtools-control-button @click="${this.#Je}" .disabled="${this.#Me}" .shape="${"square"}" .label="${t}" title="${a.Tooltip.getTooltipForActions(t,"chrome_recorder.start-recording")}"> </devtools-control-button> </div> </div> `}#s(){const t={wrapper:!0,"is-recording":this.#F,"is-playing":this.#Ce.isPlaying,"was-successful":"Success"===this.#Oe,"was-failure":"Failure"===this.#Oe};e.render(e.html` <div @click="${this.#at}" class="${e.Directives.classMap(t)}"> <div class="main"> ${this.#Et()} ${this.#Ke?e.html` <${tt.litTagName} .descriptor="${this.#Ke}"> </${tt.litTagName}> `:e.html` ${this.#bt()} ${this.#wt()} `} ${this.#Tt()} </div> </div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-recording-view",gt);var mt=Object.freeze({__proto__:null,RecordingFinishedEvent:st,PlayRecordingEvent:nt,AbortReplayEvent:at,RecordingChangedEvent:lt,AddAssertionEvent:dt,RecordingTitleChangedEvent:ct,NetworkConditionsChanged:pt,TimeoutChanged:ht,RecordingView:gt});const vt=new CSSStyleSheet;vt.replaceSync("*{margin:0;padding:0;box-sizing:border-box;font-weight:normal;font-size:inherit}:host{flex:1;display:block;overflow:auto}.wrapper{padding:24px;background-color:var(--color-background);height:100%;display:flex;flex-direction:column}.fit-content{width:fit-content}.align-right{width:auto;display:flex;flex-direction:row;justify-content:flex-end}\n/*# sourceURL=startView.css */\n");const bt={header:"Measure performance across an entire user journey",step1:"Record a common user journey on your website or app",step2:"Replay the recording to check if the flow is working",step3:"Generate a detailed performance trace or export a Puppeteer script for testing",createRecording:"Create a new recording",quickStart:"Quick start: learn the new Recorder panel in DevTools"},ft=i.i18n.registerUIStrings("panels/recorder/components/StartView.ts",bt),wt=i.i18n.getLocalizedString.bind(void 0,ft),yt="https://goo.gle/recorder-feedback";class xt extends Event{static eventName="createrecording";constructor(){super(xt.eventName)}}class St extends HTMLElement{static litTagName=e.literal`devtools-start-view`;#t=this.attachShadow({mode:"open"});connectedCallback(){this.#t.adoptedStyleSheets=[vt],o.ScheduledRender.scheduleRender(this,this.#s)}#Rt(){this.dispatchEvent(new xt)}#s=()=>{e.render(e.html` <div class="wrapper"> <${b.PanelIntroductionSteps.PanelIntroductionSteps.litTagName}> <span slot="title">${wt(bt.header)}</span> <span slot="step-1">${wt(bt.step1)}</span> <span slot="step-2">${wt(bt.step2)}</span> <span slot="step-3">${wt(bt.step3)}</span> </${b.PanelIntroductionSteps.PanelIntroductionSteps.litTagName}> <div class="fit-content"> <${r.Button.Button.litTagName} .variant="${"primary"}" @click="${this.#Rt}"> ${wt(bt.createRecording)} </${r.Button.Button.litTagName}> </div> <${v.PanelFeedback.PanelFeedback.litTagName} .data="${{feedbackUrl:yt,quickStartUrl:"https://developer.chrome.com/docs/devtools/recorder",quickStartLinkText:wt(bt.quickStart)}}"> </${v.PanelFeedback.PanelFeedback.litTagName}> <div class="align-right"> <${v.FeedbackButton.FeedbackButton.litTagName} .data="${{feedbackUrl:yt}}"> </${v.FeedbackButton.FeedbackButton.litTagName}> </div> </div> `,this.#t,{host:this})}}o.CustomElements.defineComponent("devtools-start-view",St);var $t=Object.freeze({__proto__:null,FEEDBACK_URL:yt,CreateRecordingEvent:xt,StartView:St});const kt=new CSSStyleSheet;kt.replaceSync("*{box-sizing:border-box;padding:0;margin:0;font-size:inherit}:host{display:block}.row{display:flex;flex-direction:row;color:var(--color-syntax-1);font-family:var(--monospace-font-family);font-size:var(--monospace-font-size);align-items:center;line-height:18px;margin-top:3px}.row devtools-button{line-height:1;margin-left:0.5em}.separator{margin-right:0.5em;color:var(--color-text-primary)}.padded{margin-left:2em}.padded.double{margin-left:4em}.selector-picker{width:18px;height:18px}.inline-button{width:18px;height:18px;opacity:0%;visibility:hidden;transition:opacity 200ms;flex-shrink:0}.row:focus-within .inline-button,\n.row:hover .inline-button{opacity:100%;visibility:visible}.wrapped.row{flex-wrap:wrap}.gap.row{gap:5px}.gap.row devtools-button{margin-left:0}.regular-font{font-family:inherit;font-size:inherit}.no-margin{margin:0}.row-buttons{margin-top:3px}.error{margin:3px 0 6px;padding:8px 12px;background:var(--color-error-background);color:var(--color-error-text)}\n/*# sourceURL=stepEditor.css */\n");var Et=self&&self.__decorate||function(e,t,i,r){var o,s=arguments.length,n=s<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,r);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(n=(s<3?o(n):s>3?o(t,i,n):o(t,i))||n);return s>3&&n&&Object.defineProperty(t,i,n),n};const{html:Tt,Decorators:Rt,Directives:Ct,LitElement:Nt}=e,{customElement:Mt,property:It,state:Bt}=Rt,{live:At}=Ct,Pt=Object.freeze({string:e=>e.trim(),number:e=>{const t=parseFloat(e);return Number.isNaN(t)?0:t},boolean:e=>"true"===e.toLowerCase()}),zt=Object.freeze({selectors:"string",offsetX:"number",offsetY:"number",target:"string",frame:"number",assertedEvents:"string",value:"string",key:"string",operator:"string",count:"number",expression:"string",x:"number",y:"number",url:"string",type:"string",timeout:"number",duration:"number",button:"string",deviceType:"string",width:"number",height:"number",deviceScaleFactor:"number",isMobile:"boolean",hasTouch:"boolean",isLandscape:"boolean",download:"number",upload:"number",latency:"number",name:"string",parameters:"string",visible:"boolean",properties:"string",attributes:"string"}),Ot=j({selectors:[[".cls"]],offsetX:1,offsetY:1,target:"main",frame:[0],assertedEvents:[{type:"navigation",url:"https://example.com",title:"Title"}],value:"Value",key:"Enter",operator:">=",count:1,expression:"true",x:0,y:0,url:"https://example.com",timeout:5e3,duration:50,deviceType:"mouse",button:"primary",type:"click",width:800,height:600,deviceScaleFactor:1,isMobile:!1,hasTouch:!1,isLandscape:!0,download:1e3,upload:1e3,latency:25,name:"customParam",parameters:"{}",properties:"{}",attributes:[{name:"attribute",value:"value"}],visible:!0}),Lt=j({[a.Schema.StepType.Click]:{required:["selectors","offsetX","offsetY"],optional:["assertedEvents","button","deviceType","duration","frame","target","timeout"]},[a.Schema.StepType.DoubleClick]:{required:["offsetX","offsetY","selectors"],optional:["assertedEvents","button","deviceType","frame","target","timeout"]},[a.Schema.StepType.Hover]:{required:["selectors"],optional:["assertedEvents","frame","target","timeout"]},[a.Schema.StepType.Change]:{required:["selectors","value"],optional:["assertedEvents","frame","target","timeout"]},[a.Schema.StepType.KeyDown]:{required:["key"],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.KeyUp]:{required:["key"],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.Scroll]:{required:[],optional:["assertedEvents","frame","target","timeout","x","y"]},[a.Schema.StepType.Close]:{required:[],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.Navigate]:{required:["url"],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.WaitForElement]:{required:["selectors"],optional:["assertedEvents","attributes","count","frame","operator","properties","target","timeout","visible"]},[a.Schema.StepType.WaitForExpression]:{required:["expression"],optional:["assertedEvents","frame","target","timeout"]},[a.Schema.StepType.CustomStep]:{required:["name","parameters"],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.EmulateNetworkConditions]:{required:["download","latency","upload"],optional:["assertedEvents","target","timeout"]},[a.Schema.StepType.SetViewport]:{required:["deviceScaleFactor","hasTouch","height","isLandscape","isMobile","width"],optional:["assertedEvents","target","timeout"]}}),Dt={notSaved:"Not saved: {error}",addAttribute:"Add {attributeName}",deleteRow:"Delete row",selectorPicker:"Select an element in the page to update selectors",addFrameIndex:"Add frame index within the frame tree",removeFrameIndex:"Remove frame index",addSelectorPart:"Add a selector part",removeSelectorPart:"Remove a selector part",addSelector:"Add a selector",removeSelector:"Remove a selector",unknownActionType:"Unknown action type."},Ft=i.i18n.registerUIStrings("panels/recorder/components/StepEditor.ts",Dt),jt=i.i18n.getLocalizedString.bind(void 0,Ft);class _t extends Event{static eventName="stepedited";data;constructor(e){super(_t.eventName,{bubbles:!0,composed:!0}),this.data=e}}class Ut{static#Ct=new w.SharedObject.SharedObject((()=>a.RecordingPlayer.RecordingPlayer.connectPuppeteer()),(({browser:e})=>a.RecordingPlayer.RecordingPlayer.disconnectPuppeteer(e)));static async default(e){const t={type:e},i=Lt[t.type];let r=Promise.resolve();for(const e of i.required)r=Promise.all([r,(async()=>Object.assign(t,{[e]:await this.defaultByAttribute(t,e)}))()]);return await r,Object.freeze(t)}static async defaultByAttribute(e,t){return this.#Ct.run((e=>{switch(t){case"assertedEvents":return V(Ot.assertedEvents,new U({0:{url:e.page.url()||Ot.assertedEvents[0].url}}));case"url":return e.page.url()||Ot.url;case"height":return e.page.evaluate((()=>visualViewport.height))||Ot.height;case"width":return e.page.evaluate((()=>visualViewport.width))||Ot.width;default:return Ot[t]}}))}static fromStep(e){const t=structuredClone(e);for(const i of["parameters","properties"])i in e&&void 0!==e[i]&&(t[i]=JSON.stringify(e[i]));if("attributes"in e&&e.attributes){t.attributes=[];for(const[i,r]of Object.entries(e.attributes))t.attributes.push({name:i,value:r})}return"selectors"in e&&(t.selectors=e.selectors.map((e=>"string"==typeof e?[e]:[...e]))),j(t)}static toStep(e){const t=structuredClone(e);for(const i of["parameters","properties"]){const r=e[i];r&&Object.assign(t,{[i]:JSON.parse(r)})}if(e.attributes)if(0!==e.attributes.length){const i={};for(const{name:t,value:r}of e.attributes)Object.assign(i,{[t]:r});Object.assign(t,{attributes:i})}else"attributes"in t&&delete t.attributes;if(e.selectors){const i=e.selectors.filter((e=>e.length>0)).map((e=>1===e.length?e[0]:[...e]));0!==i.length?Object.assign(t,{selectors:i}):"selectors"in t&&delete t.selectors}return e.frame&&0===e.frame.length&&"frame"in t&&delete t.frame,i=a.SchemaUtils.parseStep(t),JSON.parse(JSON.stringify(i));var i}}let Vt=class extends Nt{static styles=[kt];#Nt=new f.SelectorPicker.SelectorPicker(this);constructor(){super(),this.disabled=!1}#e=e=>{e.preventDefault(),e.stopPropagation(),this.#Nt.toggle()};disconnectedCallback(){super.disconnectedCallback(),this.#Nt.stop()}render(){if(!this.disabled)return Tt`<devtools-button @click="${this.#e}" .title="${jt(Dt.selectorPicker)}" class="selector-picker" .size="${"SMALL"}" .iconName="${"select-element"}" .active="${this.#Nt.active}" .variant="${"secondary"}"></devtools-button>`}};Et([It()],Vt.prototype,"disabled",void 0),Vt=Et([Mt("devtools-recorder-selector-picker-button")],Vt);let qt=class extends Nt{static styles=[kt];#Mt=new Set;constructor(){super(),this.state={type:a.Schema.StepType.WaitForElement},this.isTypeEditable=!0,this.disabled=!1}createRenderRoot(){const e=super.createRenderRoot();return e.addEventListener("keydown",this.#h),e}set step(e){this.state=j(Ut.fromStep(e)),this.error=void 0}#It(e){try{this.dispatchEvent(new _t(Ut.toStep(e))),this.state=e}catch(e){this.error=e.message}}#Bt=e=>{e.preventDefault(),e.stopPropagation(),this.#It(V(this.state,{target:e.data.target,frame:e.data.frame,selectors:e.data.selectors.map((e=>"string"==typeof e?[e]:e)),offsetX:e.data.offsetX,offsetY:e.data.offsetY}))};#At=(e,t,i)=>r=>{r.preventDefault(),r.stopPropagation(),this.#It(V(this.state,e)),this.#Pt(t),i&&d.userMetrics.recordingEdited(i)};#h=e=>{if(F(e instanceof KeyboardEvent),e.target instanceof oe&&"Enter"===e.key){e.preventDefault(),e.stopPropagation();const t=this.renderRoot.querySelectorAll("devtools-recorder-input"),i=[...t].findIndex((t=>t===e.target));i>=0&&i+1<t.length?t[i+1].focus():e.target.blur()}};#zt=e=>t=>{if(F(t.target instanceof oe),t.target.disabled)return;const i=zt[e.attribute],r=Pt[i](t.target.value),o=e.from.bind(this)(r);o&&(this.#It(V(this.state,o)),e.metric&&d.userMetrics.recordingEdited(e.metric))};#Ot=async e=>{if(F(e.target instanceof oe),e.target.disabled)return;const t=e.target.value;t!==this.state.type&&(Object.values(a.Schema.StepType).includes(t)?(this.#It(await Ut.default(t)),d.userMetrics.recordingEdited(d.UserMetrics.RecordingEdited.TypeChanged)):this.error=jt(Dt.unknownActionType))};#Lt=async e=>{e.preventDefault(),e.stopPropagation();const t=e.target.dataset.attribute;this.#It(V(this.state,{[t]:await Ut.defaultByAttribute(this.state,t)})),this.#Pt(`[data-attribute=${t}].attribute devtools-recorder-input`)};#Dt(e){if(!this.disabled)return Tt` <devtools-button title="${e.title}" .size="${"SMALL"}" .iconName="${e.iconName}" .variant="${"secondary"}" class="inline-button ${e.class}" @click="${e.onClick}"></devtools-button> `}#Ft(e){if(this.disabled)return;return[...Lt[this.state.type].optional].includes(e)&&!this.disabled?Tt`<devtools-button .size="${"SMALL"}" .iconName="${"bin"}" .variant="${"secondary"}" .title="${jt(Dt.deleteRow)}" class="inline-button delete-row" data-attribute="${e}" @click="${t=>{t.preventDefault(),t.stopPropagation(),this.#It(V(this.state,{[e]:void 0}))}}"></devtools-button>`:void 0}#jt(e){return this.#Mt.add("type"),Tt`<div class="row attribute" data-attribute="type"> <div>type<span class="separator">:</span></div> <devtools-recorder-input .disabled="${!e||this.disabled}" .options="${Object.values(a.Schema.StepType)}" .placeholder="${Ot.type}" .value="${At(this.state.type)}" @blur="${this.#Ot}"></devtools-recorder-input> </div>`}#_t(e){this.#Mt.add(e);const t=this.state[e]?.toString();if(void 0!==t)return Tt`<div class="row attribute" data-attribute="${e}"> <div>${e}<span class="separator">:</span></div> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot[e].toString()}" .value="${At(t)}" .mimeType="${(()=>{switch(e){case"expression":return"text/javascript";case"properties":return"application/json";default:return""}})()}" @blur="${this.#zt({attribute:e,from(t){if(void 0!==this.state[e]){if("properties"===e)d.userMetrics.recordingAssertion(d.UserMetrics.RecordingAssertion.PropertyAssertionEdited);return{[e]:t}}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> ${this.#Ft(e)} </div>`}#Ut(){if(this.#Mt.add("frame"),void 0!==this.state.frame)return Tt` <div class="attribute" data-attribute="frame"> <div class="row"> <div>frame<span class="separator">:</span></div> ${this.#Ft("frame")} </div> ${this.state.frame.map(((e,t,i)=>Tt` <div class="padded row"> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.frame[0].toString()}" .value="${At(e.toString())}" data-path="${`frame.${t}`}" @blur="${this.#zt({attribute:"frame",from(e){if(void 0!==this.state.frame?.[t])return{frame:new U({[t]:e})}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> ${this.#Dt({class:"add-frame",title:jt(Dt.addFrameIndex),iconName:"plus",onClick:this.#At({frame:new U({[t+1]:new _(Ot.frame[0])})},`devtools-recorder-input[data-path="frame.${t+1}"]`,d.UserMetrics.RecordingEdited.OtherEditing)})} ${this.#Dt({class:"remove-frame",title:jt(Dt.removeFrameIndex),iconName:"minus",onClick:this.#At({frame:new U({[t]:void 0})},`devtools-recorder-input[data-path="frame.${Math.min(t,i.length-2)}"]`,d.UserMetrics.RecordingEdited.OtherEditing)})} </div> `))} </div> `}#Vt(){if(this.#Mt.add("selectors"),void 0!==this.state.selectors)return Tt`<div class="attribute" data-attribute="selectors"> <div class="row"> <div>selectors<span class="separator">:</span></div> <devtools-recorder-selector-picker-button @selectorpicked="${this.#Bt}" .disabled="${this.disabled}"></devtools-recorder-selector-picker-button> ${this.#Ft("selectors")} </div> ${this.state.selectors.map(((e,t,i)=>Tt`<div class="padded row" data-selector-path="${t}"> <div>selector #${t+1}<span class="separator">:</span></div> ${this.#Dt({class:"add-selector",title:jt(Dt.addSelector),iconName:"plus",onClick:this.#At({selectors:new U({[t+1]:new _(structuredClone(Ot.selectors[0]))})},`devtools-recorder-input[data-path="selectors.${t+1}.0"]`,d.UserMetrics.RecordingEdited.SelectorAdded)})} ${this.#Dt({class:"remove-selector",title:jt(Dt.removeSelector),iconName:"minus",onClick:this.#At({selectors:new U({[t]:void 0})},`devtools-recorder-input[data-path="selectors.${Math.min(t,i.length-2)}.0"]`,d.UserMetrics.RecordingEdited.SelectorRemoved)})} </div> ${e.map(((e,i,r)=>Tt`<div class="double padded row" data-selector-path="${t}.${i}"> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.selectors[0][0]}" .value="${At(e)}" data-path="${`selectors.${t}.${i}`}" @blur="${this.#zt({attribute:"selectors",from(e){if(void 0!==this.state.selectors?.[t]?.[i])return{selectors:new U({[t]:new U({[i]:e})})}},metric:d.UserMetrics.RecordingEdited.SelectorPartEdited})}"></devtools-recorder-input> ${this.#Dt({class:"add-selector-part",title:jt(Dt.addSelectorPart),iconName:"plus",onClick:this.#At({selectors:new U({[t]:new U({[i+1]:new _(Ot.selectors[0][0])})})},`devtools-recorder-input[data-path="selectors.${t}.${i+1}"]`,d.UserMetrics.RecordingEdited.SelectorPartAdded)})} ${this.#Dt({class:"remove-selector-part",title:jt(Dt.removeSelectorPart),iconName:"minus",onClick:this.#At({selectors:new U({[t]:new U({[i]:void 0})})},`devtools-recorder-input[data-path="selectors.${t}.${Math.min(i,r.length-2)}"]`,d.UserMetrics.RecordingEdited.SelectorPartRemoved)})} </div>`))}`))} </div>`}#qt(){if(this.#Mt.add("assertedEvents"),void 0!==this.state.assertedEvents)return Tt`<div class="attribute" data-attribute="assertedEvents"> <div class="row"> <div>asserted events<span class="separator">:</span></div> ${this.#Ft("assertedEvents")} </div> ${this.state.assertedEvents.map(((e,t)=>Tt` <div class="padded row"> <div>type<span class="separator">:</span></div> <div>${e.type}</div> </div> <div class="padded row"> <div>title<span class="separator">:</span></div> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.assertedEvents[0].title}" .value="${At(e.title??"")}" @blur="${this.#zt({attribute:"assertedEvents",from(e){if(void 0!==this.state.assertedEvents?.[t]?.title)return{assertedEvents:new U({[t]:{title:e}})}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> </div> <div class="padded row"> <div>url<span class="separator">:</span></div> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.assertedEvents[0].url}" .value="${At(e.url??"")}" @blur="${this.#zt({attribute:"url",from(e){if(void 0!==this.state.assertedEvents?.[t]?.url)return{assertedEvents:new U({[t]:{url:e}})}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> </div>`))} </div> `}#Gt(){if(this.#Mt.add("attributes"),void 0!==this.state.attributes)return Tt`<div class="attribute" data-attribute="attributes"> <div class="row"> <div>attributes<span class="separator">:</span></div> ${this.#Ft("attributes")} </div> ${this.state.attributes.map((({name:e,value:t},i,r)=>Tt`<div class="padded row"> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.attributes[0].name}" .value="${At(e)}" data-path="${`attributes.${i}.name`}" @blur="${this.#zt({attribute:"attributes",from(e){if(void 0!==this.state.attributes?.[i]?.name)return d.userMetrics.recordingAssertion(d.UserMetrics.RecordingAssertion.AttributeAssertionEdited),{attributes:new U({[i]:{name:e}})}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> <span class="separator">:</span> <devtools-recorder-input .disabled="${this.disabled}" .placeholder="${Ot.attributes[0].value}" .value="${At(t)}" data-path="${`attributes.${i}.value`}" @blur="${this.#zt({attribute:"attributes",from(e){if(void 0!==this.state.attributes?.[i]?.value)return d.userMetrics.recordingAssertion(d.UserMetrics.RecordingAssertion.AttributeAssertionEdited),{attributes:new U({[i]:{value:e}})}},metric:d.UserMetrics.RecordingEdited.OtherEditing})}"></devtools-recorder-input> ${this.#Dt({class:"add-attribute-assertion",title:jt(Dt.addSelectorPart),iconName:"plus",onClick:this.#At({attributes:new U({[i+1]:new _((()=>{{const e=new Set(r.map((({name:e})=>e))),t=Ot.attributes[0];let i=t.name,o=0;for(;e.has(i);)++o,i=`${t.name}-${o}`;return{...t,name:i}}})())})},`devtools-recorder-input[data-path="attributes.${i+1}.name"]`,d.UserMetrics.RecordingEdited.OtherEditing)})} ${this.#Dt({class:"remove-attribute-assertion",title:jt(Dt.removeSelectorPart),iconName:"minus",onClick:this.#At({attributes:new U({[i]:void 0})},`devtools-recorder-input[data-path="attributes.${Math.min(i,r.length-2)}.value"]`,d.UserMetrics.RecordingEdited.OtherEditing)})} </div>`))} </div>`}#Kt(){return[...Lt[this.state.type].optional].filter((e=>void 0===this.state[e])).map((e=>Tt`<devtools-button .variant="${"secondary"}" class="add-row" data-attribute="${e}" @click="${this.#Lt}"> ${jt(Dt.addAttribute,{attributeName:e})} </devtools-button>`))}#Pt=e=>{this.updateComplete.then((()=>{this.renderRoot.querySelector(e)?.focus()}))};render(){this.#Mt=new Set;return Tt` <div class="wrapper"> ${this.#jt(this.isTypeEditable)} ${this.#_t("target")} ${this.#Ut()} ${this.#Vt()} ${this.#_t("deviceType")} ${this.#_t("button")} ${this.#_t("url")} ${this.#_t("x")} ${this.#_t("y")} ${this.#_t("offsetX")} ${this.#_t("offsetY")} ${this.#_t("value")} ${this.#_t("key")} ${this.#_t("operator")} ${this.#_t("count")} ${this.#_t("expression")} ${this.#_t("duration")} ${this.#qt()} ${this.#_t("timeout")} ${this.#_t("width")} ${this.#_t("height")} ${this.#_t("deviceScaleFactor")} ${this.#_t("isMobile")} ${this.#_t("hasTouch")} ${this.#_t("isLandscape")} ${this.#_t("download")} ${this.#_t("upload")} ${this.#_t("latency")} ${this.#_t("name")} ${this.#_t("parameters")} ${this.#_t("visible")} ${this.#_t("properties")} ${this.#Gt()} ${this.error?Tt` <div class="error"> ${jt(Dt.notSaved,{error:this.error})} </div> `:void 0} ${this.disabled?void 0:Tt`<div class="row-buttons wrapped gap row regular-font no-margin"> ${this.#Kt()} </div>`} </div> `}};Et([Bt()],qt.prototype,"state",void 0),Et([Bt()],qt.prototype,"error",void 0),Et([It()],qt.prototype,"isTypeEditable",void 0),Et([It()],qt.prototype,"disabled",void 0),qt=Et([Mt("devtools-recorder-step-editor")],qt);var Gt=Object.freeze({__proto__:null,StepEditedEvent:_t,EditorState:Ut,get StepEditor(){return qt}});export{C as ControlButton,O as CreateRecordingView,se as RecorderInput,me as RecordingListView,mt as RecordingView,Ge as ReplayButton,Le as SelectButton,We as SplitView,$t as StartView,Gt as StepEditor,Ae as StepView,ye as TimelineSection};
