import * as React from 'react';
/**
 * TypeScript generated a large union of props from `ViewProps` in
 * `d.ts` files when using `React.forwardRef`. To prevent this
 * `ForwardRefComponent` was created and exported. Use this
 * `forwardRef` instead of `React.forwardRef` so you don't have to
 * import `ForwardRefComponent`.
 * More info: https://github.com/callstack/react-native-paper/pull/3603
 */
export const forwardRef = React.forwardRef;
//# sourceMappingURL=forwardRef.js.map