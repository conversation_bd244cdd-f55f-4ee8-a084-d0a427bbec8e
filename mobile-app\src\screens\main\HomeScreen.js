import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Searchbar,
  Chip,
  ActivityIndicator,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { theme } from '../../styles/theme';
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';
import apiService from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';
import BannerSlider from '../../components/BannerSlider';
import EnhancedSearchBar from '../../components/EnhancedSearchBar';
import ProductImage, { CategoryImage, ProductCardImage } from '../../components/ProductImage';

const HomeScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user } = useAuth();
  const { totalItems } = useCart();

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setIsLoading(true);
      
      const [featuredResponse, categoriesResponse] = await Promise.all([
        apiService.get(API_ENDPOINTS.PRODUCTS.FEATURED, { limit: 10 }),
        apiService.get(API_ENDPOINTS.CATEGORIES.POPULAR, { limit: 8 }),
      ]);

      if (featuredResponse.success) {
        setFeaturedProducts(featuredResponse.data);
      }

      if (categoriesResponse.success) {
        setCategories(categoriesResponse.data);
      }
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigation.navigate('Search', { query: searchQuery.trim() });
    }
  };

  const renderFeaturedProduct = ({ item }) => (
    <ProductCardImage
      product={{
        ...item,
        image: item.image_url,
        price: item.sale_price || item.price,
        original_price: item.sale_price ? item.price : null,
      }}
      style={styles.productCard}
      onPress={() => navigation.navigate('ProductDetail', { productId: item.id })}
    />
  );

  const renderCategory = ({ item }) => (
    <CategoryImage
      category={{
        ...item,
        image: item.image_url,
      }}
      style={styles.categoryItem}
      onPress={() => navigation.navigate('Products', {
        screen: 'ProductsMain',
        params: { categoryId: item.id, categoryName: item.name }
      })}
    />
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>
              Welcome{user ? `, ${user.first_name}` : ''}!
            </Text>
            <Text style={styles.headerSubtitle}>
              Find premium puja samagri for your spiritual needs
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate('Cart')}
          >
            <Ionicons name="bag-outline" size={24} color={theme.colors.surface} />
            {totalItems > 0 && (
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{totalItems}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Enhanced Search Bar */}
        <EnhancedSearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
          placeholder="Search puja items, incense, oils..."
          suggestions={[]} // You can add dynamic suggestions here
          recentSearches={[]} // You can add recent searches here
          onSuggestionPress={(suggestion) => {
            setSearchQuery(suggestion);
            navigation.navigate('Search', { query: suggestion });
          }}
        />

        {/* Banner Slider */}
        <BannerSlider
          onBannerPress={(banner) => {
            // Handle banner press - navigate to specific category or product
            console.log('Banner pressed:', banner);
          }}
        />
      </LinearGradient>

      {/* Categories Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Products', { screen: 'Categories' })}
            labelStyle={styles.seeAllText}
          >
            See All
          </Button>
        </View>
        
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />
      </View>

      {/* Featured Products Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Featured Products</Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Products')}
            labelStyle={styles.seeAllText}
          >
            See All
          </Button>
        </View>
        
        <FlatList
          data={featuredProducts}
          renderItem={renderFeaturedProduct}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.productsList}
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => navigation.navigate('Profile', { screen: 'Orders' })}
          >
            <Ionicons name="receipt-outline" size={32} color={theme.colors.primary} />
            <Text style={styles.quickActionText}>My Orders</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => navigation.navigate('Profile', { screen: 'Addresses' })}
          >
            <Ionicons name="location-outline" size={32} color={theme.colors.primary} />
            <Text style={styles.quickActionText}>Addresses</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => navigation.navigate('Profile', { screen: 'ProfileMain' })}
          >
            <Ionicons name="person-outline" size={32} color={theme.colors.primary} />
            <Text style={styles.quickActionText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.lg,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.surface,
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    fontSize: 14,
    color: theme.colors.surface,
    opacity: 0.9,
  },
  cartButton: {
    position: 'relative',
    padding: theme.spacing.sm,
  },
  cartBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: theme.colors.surface,
    fontSize: 12,
    fontWeight: 'bold',
  },
  searchBar: {
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  searchInput: {
    fontSize: 16,
  },
  section: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  seeAllText: {
    color: theme.colors.primary,
    fontSize: 14,
  },
  categoriesList: {
    paddingRight: theme.spacing.lg,
  },
  categoryItem: {
    marginRight: theme.spacing.md,
  },
  categoryCard: {
    alignItems: 'center',
    width: 80,
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: theme.spacing.sm,
  },
  categoryName: {
    fontSize: 12,
    textAlign: 'center',
    color: theme.colors.text,
  },
  productsList: {
    paddingRight: theme.spacing.lg,
  },
  productCard: {
    marginRight: theme.spacing.md,
    width: 160,
  },
  card: {
    elevation: 2,
  },
  productImage: {
    width: '100%',
    height: 120,
  },
  productContent: {
    padding: theme.spacing.sm,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  originalPrice: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textDecorationLine: 'line-through',
    marginLeft: theme.spacing.xs,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.md,
  },
  quickActionItem: {
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  quickActionText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
});

export default HomeScreen;
