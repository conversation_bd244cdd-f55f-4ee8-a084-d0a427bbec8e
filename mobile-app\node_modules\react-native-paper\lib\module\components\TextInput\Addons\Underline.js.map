{"version": 3, "names": ["React", "Animated", "StyleSheet", "useInternalTheme", "Underline", "parentState", "error", "colors", "activeColor", "underlineColorCustom", "hasActiveOutline", "style", "theme", "themeOverrides", "isV3", "backgroundColor", "focused", "activeScale", "createElement", "View", "testID", "styles", "underline", "md3Underline", "transform", "scaleY", "create", "position", "left", "right", "bottom", "height", "zIndex"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Addons/Underline.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,QAA8B,cAAc;AAIzE,SAASC,gBAAgB,QAAQ,uBAAuB;AAiBxD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EACxBC,WAAW;EACXC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXC,oBAAoB;EACpBC,gBAAgB;EAChBC,KAAK;EACLC,KAAK,EAAEC;AACO,CAAC,KAAK;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAGX,gBAAgB,CAACU,cAAc,CAAC;EAEjD,IAAIE,eAAe,GAAGV,WAAW,CAACW,OAAO,GACrCR,WAAW,GACXC,oBAAoB;EAExB,IAAIH,KAAK,EAAES,eAAe,GAAGR,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAED,KAAK;EAE1C,MAAMW,WAAW,GAAGH,IAAI,GAAG,CAAC,GAAG,CAAC;EAEhC,oBACEd,KAAA,CAAAkB,aAAA,CAACjB,QAAQ,CAACkB,IAAI;IACZC,MAAM,EAAC,sBAAsB;IAC7BT,KAAK,EAAE,CACLU,MAAM,CAACC,SAAS,EAChBR,IAAI,IAAIO,MAAM,CAACE,YAAY,EAC3B;MACER,eAAe;MACf;MACAS,SAAS,EAAE,CACT;QACEC,MAAM,EAAE,CAACX,IAAI,GAAGJ,gBAAgB,GAAGL,WAAW,CAACW,OAAO,IAClDC,WAAW,GACX;MACN,CAAC;IAEL,CAAC,EACDN,KAAK;EACL,CACH,CAAC;AAEN,CAAC;AAED,MAAMU,MAAM,GAAGnB,UAAU,CAACwB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACDT,YAAY,EAAE;IACZQ,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}