{"version": 3, "file": "withIosBaseMods.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_plist", "_assert", "_fs", "_interopRequireWildcard", "_path", "_xcode", "_createBaseMod", "_ios", "_Entitlements", "_Xcodeproj", "_getInfoPlistPath", "_modules", "_sortObject", "_warnings", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "readFile", "writeFile", "promises", "getEntitlementsPlistTemplate", "getInfoPlistTemplate", "CFBundleDevelopmentRegion", "CFBundleExecutable", "CFBundleIdentifier", "CFBundleName", "CFBundlePackageType", "CFBundleInfoDictionaryVersion", "CFBundleSignature", "LSRequiresIPhoneOS", "NSAppTransportSecurity", "NSAllowsArbitraryLoads", "NSExceptionDomains", "localhost", "NSExceptionAllowsInsecureHTTPLoads", "UILaunchStoryboardName", "UIRequiredDeviceCapabilities", "UIViewControllerBasedStatusBarAppearance", "UIStatusBarStyle", "CADisableMinimumFrameDurationOnPhone", "defaultProviders", "dangerous", "provider", "getFilePath", "read", "write", "finalized", "appDelegate", "modRequest", "projectRoot", "Paths", "getAppDelegateFilePath", "filePath", "getFileInfo", "modResults", "contents", "expoPlist", "isIntrospective", "platformProjectRoot", "projectName", "supportingDirectory", "path", "join", "resolve", "introspect", "plist", "parse", "error", "build", "sortObject", "xcodeproj", "getPBXProjectPath", "project", "xcode", "parseSync", "writeSync", "infoPlist", "config", "getPbxproj", "infoPlistBuildProperty", "getInfoPlistPathFromPbxproj", "infoPlistPath", "fileExists", "addWarningIOS", "getInfoPlistPath", "ios", "assert", "entitlements", "ensureApplicationTargetEntitlementsFileConfigured", "Entitlements", "getEntitlementsPath", "ignoreExistingNativeFiles", "fs", "existsSync", "podfile", "getPodfilePath", "podfileProperties", "results", "JsonFile", "readAsync", "writeAsync", "withIosBaseMods", "providers", "props", "withGeneratedBaseMods", "platform", "getIosModFileProviders"], "sources": ["../../src/plugins/withIosBaseMods.ts"], "sourcesContent": ["import JsonFile, { JSONObject, JSONValue } from '@expo/json-file';\nimport plist from '@expo/plist';\nimport assert from 'assert';\nimport fs, { promises } from 'fs';\nimport path from 'path';\nimport xcode, { XcodeProject } from 'xcode';\n\nimport { ForwardedBaseModOptions, provider, withGeneratedBaseMods } from './createBaseMod';\nimport { ExportedConfig, ModConfig } from '../Plugin.types';\nimport { Entitlements, Paths } from '../ios';\nimport { ensureApplicationTargetEntitlementsFileConfigured } from '../ios/Entitlements';\nimport { InfoPlist } from '../ios/IosConfig.types';\nimport { getPbxproj } from '../ios/utils/Xcodeproj';\nimport { getInfoPlistPathFromPbxproj } from '../ios/utils/getInfoPlistPath';\nimport { fileExists } from '../utils/modules';\nimport { sortObject } from '../utils/sortObject';\nimport { addWarningIOS } from '../utils/warnings';\n\nconst { readFile, writeFile } = promises;\n\ntype IosModName = keyof Required<ModConfig>['ios'];\n\nfunction getEntitlementsPlistTemplate() {\n  // TODO: Fetch the versioned template file if possible\n  return {};\n}\n\nfunction getInfoPlistTemplate() {\n  // TODO: Fetch the versioned template file if possible\n  return {\n    CFBundleDevelopmentRegion: '$(DEVELOPMENT_LANGUAGE)',\n    CFBundleExecutable: '$(EXECUTABLE_NAME)',\n    CFBundleIdentifier: '$(PRODUCT_BUNDLE_IDENTIFIER)',\n    CFBundleName: '$(PRODUCT_NAME)',\n    CFBundlePackageType: '$(PRODUCT_BUNDLE_PACKAGE_TYPE)',\n    CFBundleInfoDictionaryVersion: '6.0',\n    CFBundleSignature: '????',\n    LSRequiresIPhoneOS: true,\n    NSAppTransportSecurity: {\n      NSAllowsArbitraryLoads: true,\n      NSExceptionDomains: {\n        localhost: {\n          NSExceptionAllowsInsecureHTTPLoads: true,\n        },\n      },\n    },\n    UILaunchStoryboardName: 'SplashScreen',\n    UIRequiredDeviceCapabilities: ['armv7'],\n    UIViewControllerBasedStatusBarAppearance: false,\n    UIStatusBarStyle: 'UIStatusBarStyleDefault',\n    CADisableMinimumFrameDurationOnPhone: true,\n  };\n}\n\nconst defaultProviders = {\n  dangerous: provider<unknown>({\n    getFilePath() {\n      return '';\n    },\n    async read() {\n      return {};\n    },\n    async write() {},\n  }),\n  finalized: provider<unknown>({\n    getFilePath() {\n      return '';\n    },\n    async read() {\n      return {};\n    },\n    async write() {},\n  }),\n  // Append a rule to supply AppDelegate data to mods on `mods.ios.appDelegate`\n  appDelegate: provider<Paths.AppDelegateProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      // TODO: Get application AppDelegate file from pbxproj.\n      return Paths.getAppDelegateFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath: string, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n  // Append a rule to supply Expo.plist data to mods on `mods.ios.expoPlist`\n  expoPlist: provider<JSONObject>({\n    isIntrospective: true,\n    getFilePath({ modRequest: { platformProjectRoot, projectName } }) {\n      const supportingDirectory = path.join(platformProjectRoot, projectName!, 'Supporting');\n      return path.resolve(supportingDirectory, 'Expo.plist');\n    },\n    async read(filePath, { modRequest: { introspect } }) {\n      try {\n        return plist.parse(await readFile(filePath, 'utf8'));\n      } catch (error) {\n        if (introspect) {\n          return {};\n        }\n        throw error;\n      }\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) {\n        return;\n      }\n      await writeFile(filePath, plist.build(sortObject(modResults)));\n    },\n  }),\n  // Append a rule to supply .xcodeproj data to mods on `mods.ios.xcodeproj`\n  xcodeproj: provider<XcodeProject>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getPBXProjectPath(projectRoot);\n    },\n    async read(filePath) {\n      const project = xcode.project(filePath);\n      project.parseSync();\n      return project;\n    },\n    async write(filePath, { modResults }) {\n      await writeFile(filePath, modResults.writeSync());\n    },\n  }),\n  // Append a rule to supply Info.plist data to mods on `mods.ios.infoPlist`\n  infoPlist: provider<InfoPlist, ForwardedBaseModOptions>({\n    isIntrospective: true,\n    async getFilePath(config) {\n      let project: xcode.XcodeProject | null = null;\n      try {\n        project = getPbxproj(config.modRequest.projectRoot);\n      } catch {\n        // noop\n      }\n\n      // Only check / warn if a project actually exists, this'll provide\n      // more accurate warning messages for users in managed projects.\n      if (project) {\n        const infoPlistBuildProperty = getInfoPlistPathFromPbxproj(project);\n\n        if (infoPlistBuildProperty) {\n          //: [root]/myapp/ios/MyApp/Info.plist\n          const infoPlistPath = path.join(\n            //: myapp/ios\n            config.modRequest.platformProjectRoot,\n            //: MyApp/Info.plist\n            infoPlistBuildProperty\n          );\n          if (fileExists(infoPlistPath)) {\n            return infoPlistPath;\n          }\n          addWarningIOS(\n            'mods.ios.infoPlist',\n            `Info.plist file linked to Xcode project does not exist: ${infoPlistPath}`\n          );\n        } else {\n          addWarningIOS('mods.ios.infoPlist', 'Failed to find Info.plist linked to Xcode project.');\n        }\n      }\n      try {\n        // Fallback on glob...\n        return await Paths.getInfoPlistPath(config.modRequest.projectRoot);\n      } catch (error: any) {\n        if (config.modRequest.introspect) {\n          // fallback to an empty string in introspection mode.\n          return '';\n        }\n        throw error;\n      }\n    },\n    async read(filePath, config) {\n      // Apply all of the Info.plist values to the expo.ios.infoPlist object\n      // TODO: Remove this in favor of just overwriting the Info.plist with the Expo object. This will enable people to actually remove values.\n      if (!config.ios) config.ios = {};\n      if (!config.ios.infoPlist) config.ios.infoPlist = {};\n\n      let modResults: InfoPlist;\n      try {\n        const contents = await readFile(filePath, 'utf8');\n        assert(contents, 'Info.plist is empty');\n        modResults = plist.parse(contents) as InfoPlist;\n      } catch (error: any) {\n        // Throw errors in introspection mode.\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n        // Fallback to using the infoPlist object from the Expo config.\n        modResults = getInfoPlistTemplate();\n      }\n\n      config.ios.infoPlist = {\n        ...(modResults || {}),\n        ...config.ios.infoPlist,\n      };\n\n      return config.ios.infoPlist!;\n    },\n    async write(filePath, config) {\n      // Update the contents of the static infoPlist object\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.infoPlist = config.modResults;\n\n      // Return early without writing, in introspection mode.\n      if (config.modRequest.introspect) {\n        return;\n      }\n\n      await writeFile(filePath, plist.build(sortObject(config.modResults)));\n    },\n  }),\n  // Append a rule to supply .entitlements data to mods on `mods.ios.entitlements`\n  entitlements: provider<JSONObject, ForwardedBaseModOptions>({\n    isIntrospective: true,\n\n    async getFilePath(config) {\n      try {\n        ensureApplicationTargetEntitlementsFileConfigured(config.modRequest.projectRoot);\n        return Entitlements.getEntitlementsPath(config.modRequest.projectRoot) ?? '';\n      } catch (error: any) {\n        if (config.modRequest.introspect) {\n          // fallback to an empty string in introspection mode.\n          return '';\n        }\n        throw error;\n      }\n    },\n\n    async read(filePath, config) {\n      let modResults: JSONObject;\n      try {\n        if (!config.modRequest.ignoreExistingNativeFiles && fs.existsSync(filePath)) {\n          const contents = await readFile(filePath, 'utf8');\n          assert(contents, 'Entitlements plist is empty');\n          modResults = plist.parse(contents);\n        } else {\n          modResults = getEntitlementsPlistTemplate();\n        }\n      } catch (error: any) {\n        // Throw errors in introspection mode.\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n        // Fallback to using the template file.\n        modResults = getEntitlementsPlistTemplate();\n      }\n\n      // Apply all of the .entitlements values to the expo.ios.entitlements object\n      // TODO: Remove this in favor of just overwriting the .entitlements with the Expo object. This will enable people to actually remove values.\n      if (!config.ios) config.ios = {};\n      if (!config.ios.entitlements) config.ios.entitlements = {};\n\n      config.ios.entitlements = {\n        ...(modResults || {}),\n        ...config.ios.entitlements,\n      };\n\n      return config.ios.entitlements!;\n    },\n\n    async write(filePath, config) {\n      // Update the contents of the static entitlements object\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.entitlements = config.modResults;\n\n      // Return early without writing, in introspection mode.\n      if (config.modRequest.introspect) {\n        return;\n      }\n\n      await writeFile(filePath, plist.build(sortObject(config.modResults)));\n    },\n  }),\n\n  podfile: provider<Paths.PodfileProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getPodfilePath(projectRoot);\n    },\n    // @ts-expect-error\n    async read(filePath) {\n      // Note(cedric): this file is ruby, which is a 1-value subset of AppleLanguage and fails the type check\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  // Append a rule to supply Podfile.properties.json data to mods on `mods.ios.podfileProperties`\n  podfileProperties: provider<Record<string, JSONValue>>({\n    isIntrospective: true,\n\n    getFilePath({ modRequest: { platformProjectRoot } }) {\n      return path.resolve(platformProjectRoot, 'Podfile.properties.json');\n    },\n    async read(filePath) {\n      let results: Record<string, JSONValue> = {};\n      try {\n        results = await JsonFile.readAsync(filePath);\n      } catch {}\n      return results;\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) {\n        return;\n      }\n      await JsonFile.writeAsync(filePath, modResults);\n    },\n  }),\n};\n\ntype IosDefaultProviders = typeof defaultProviders;\n\nexport function withIosBaseMods(\n  config: ExportedConfig,\n  {\n    providers,\n    ...props\n  }: ForwardedBaseModOptions & { providers?: Partial<IosDefaultProviders> } = {}\n): ExportedConfig {\n  return withGeneratedBaseMods<IosModName>(config, {\n    ...props,\n    platform: 'ios',\n    providers: providers ?? getIosModFileProviders(),\n  });\n}\n\nexport function getIosModFileProviders() {\n  return defaultProviders;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,MAAA;EAAA,MAAAP,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAK,KAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,OAAA;EAAA,MAAAR,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAM,MAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAS,eAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,cAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAU,KAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,IAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,cAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,aAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAY,WAAA;EAAA,MAAAZ,IAAA,GAAAE,OAAA;EAAAU,UAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,kBAAA;EAAA,MAAAb,IAAA,GAAAE,OAAA;EAAAW,iBAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAc,SAAA;EAAA,MAAAd,IAAA,GAAAE,OAAA;EAAAY,QAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAe,YAAA;EAAA,MAAAf,IAAA,GAAAE,OAAA;EAAAa,WAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAgB,UAAA;EAAA,MAAAhB,IAAA,GAAAE,OAAA;EAAAc,SAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,SAAAiB,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAZ,wBAAAY,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAzB,uBAAAqC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAElD,MAAM;EAAEC,QAAQ;EAAEC;AAAU,CAAC,GAAGC,cAAQ;AAIxC,SAASC,4BAA4BA,CAAA,EAAG;EACtC;EACA,OAAO,CAAC,CAAC;AACX;AAEA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B;EACA,OAAO;IACLC,yBAAyB,EAAE,yBAAyB;IACpDC,kBAAkB,EAAE,oBAAoB;IACxCC,kBAAkB,EAAE,8BAA8B;IAClDC,YAAY,EAAE,iBAAiB;IAC/BC,mBAAmB,EAAE,gCAAgC;IACrDC,6BAA6B,EAAE,KAAK;IACpCC,iBAAiB,EAAE,MAAM;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,sBAAsB,EAAE;MACtBC,sBAAsB,EAAE,IAAI;MAC5BC,kBAAkB,EAAE;QAClBC,SAAS,EAAE;UACTC,kCAAkC,EAAE;QACtC;MACF;IACF,CAAC;IACDC,sBAAsB,EAAE,cAAc;IACtCC,4BAA4B,EAAE,CAAC,OAAO,CAAC;IACvCC,wCAAwC,EAAE,KAAK;IAC/CC,gBAAgB,EAAE,yBAAyB;IAC3CC,oCAAoC,EAAE;EACxC,CAAC;AACH;AAEA,MAAMC,gBAAgB,GAAG;EACvBC,SAAS,EAAE,IAAAC,yBAAQ,EAAU;IAC3BC,WAAWA,CAAA,EAAG;MACZ,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAO,CAAC,CAAC;IACX,CAAC;IACD,MAAMC,KAAKA,CAAA,EAAG,CAAC;EACjB,CAAC,CAAC;EACFC,SAAS,EAAE,IAAAJ,yBAAQ,EAAU;IAC3BC,WAAWA,CAAA,EAAG;MACZ,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAO,CAAC,CAAC;IACX,CAAC;IACD,MAAMC,KAAKA,CAAA,EAAG,CAAC;EACjB,CAAC,CAAC;EACF;EACAE,WAAW,EAAE,IAAAL,yBAAQ,EAA+B;IAClDC,WAAWA,CAAC;MAAEK,UAAU,EAAE;QAAEC;MAAY;IAAE,CAAC,EAAE;MAC3C;MACA,OAAOC,YAAK,CAACC,sBAAsB,CAACF,WAAW,CAAC;IAClD,CAAC;IACD,MAAML,IAAIA,CAACQ,QAAQ,EAAE;MACnB,OAAOF,YAAK,CAACG,WAAW,CAACD,QAAQ,CAAC;IACpC,CAAC;IACD,MAAMP,KAAKA,CAACO,QAAgB,EAAE;MAAEE,UAAU,EAAE;QAAEC;MAAS;IAAE,CAAC,EAAE;MAC1D,MAAMrC,SAAS,CAACkC,QAAQ,EAAEG,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EACF;EACAC,SAAS,EAAE,IAAAd,yBAAQ,EAAa;IAC9Be,eAAe,EAAE,IAAI;IACrBd,WAAWA,CAAC;MAAEK,UAAU,EAAE;QAAEU,mBAAmB;QAAEC;MAAY;IAAE,CAAC,EAAE;MAChE,MAAMC,mBAAmB,GAAGC,eAAI,CAACC,IAAI,CAACJ,mBAAmB,EAAEC,WAAW,EAAG,YAAY,CAAC;MACtF,OAAOE,eAAI,CAACE,OAAO,CAACH,mBAAmB,EAAE,YAAY,CAAC;IACxD,CAAC;IACD,MAAMhB,IAAIA,CAACQ,QAAQ,EAAE;MAAEJ,UAAU,EAAE;QAAEgB;MAAW;IAAE,CAAC,EAAE;MACnD,IAAI;QACF,OAAOC,gBAAK,CAACC,KAAK,CAAC,MAAMjD,QAAQ,CAACmC,QAAQ,EAAE,MAAM,CAAC,CAAC;MACtD,CAAC,CAAC,OAAOe,KAAK,EAAE;QACd,IAAIH,UAAU,EAAE;UACd,OAAO,CAAC,CAAC;QACX;QACA,MAAMG,KAAK;MACb;IACF,CAAC;IACD,MAAMtB,KAAKA,CAACO,QAAQ,EAAE;MAAEE,UAAU;MAAEN,UAAU,EAAE;QAAEgB;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;QACd;MACF;MACA,MAAM9C,SAAS,CAACkC,QAAQ,EAAEa,gBAAK,CAACG,KAAK,CAAC,IAAAC,wBAAU,EAACf,UAAU,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;EACF;EACAgB,SAAS,EAAE,IAAA5B,yBAAQ,EAAe;IAChCC,WAAWA,CAAC;MAAEK,UAAU,EAAE;QAAEC;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOC,YAAK,CAACqB,iBAAiB,CAACtB,WAAW,CAAC;IAC7C,CAAC;IACD,MAAML,IAAIA,CAACQ,QAAQ,EAAE;MACnB,MAAMoB,OAAO,GAAGC,gBAAK,CAACD,OAAO,CAACpB,QAAQ,CAAC;MACvCoB,OAAO,CAACE,SAAS,CAAC,CAAC;MACnB,OAAOF,OAAO;IAChB,CAAC;IACD,MAAM3B,KAAKA,CAACO,QAAQ,EAAE;MAAEE;IAAW,CAAC,EAAE;MACpC,MAAMpC,SAAS,CAACkC,QAAQ,EAAEE,UAAU,CAACqB,SAAS,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,CAAC;EACF;EACAC,SAAS,EAAE,IAAAlC,yBAAQ,EAAqC;IACtDe,eAAe,EAAE,IAAI;IACrB,MAAMd,WAAWA,CAACkC,MAAM,EAAE;MACxB,IAAIL,OAAkC,GAAG,IAAI;MAC7C,IAAI;QACFA,OAAO,GAAG,IAAAM,uBAAU,EAACD,MAAM,CAAC7B,UAAU,CAACC,WAAW,CAAC;MACrD,CAAC,CAAC,MAAM;QACN;MAAA;;MAGF;MACA;MACA,IAAIuB,OAAO,EAAE;QACX,MAAMO,sBAAsB,GAAG,IAAAC,+CAA2B,EAACR,OAAO,CAAC;QAEnE,IAAIO,sBAAsB,EAAE;UAC1B;UACA,MAAME,aAAa,GAAGpB,eAAI,CAACC,IAAI;UAC7B;UACAe,MAAM,CAAC7B,UAAU,CAACU,mBAAmB;UACrC;UACAqB,sBACF,CAAC;UACD,IAAI,IAAAG,qBAAU,EAACD,aAAa,CAAC,EAAE;YAC7B,OAAOA,aAAa;UACtB;UACA,IAAAE,yBAAa,EACX,oBAAoB,EACpB,2DAA2DF,aAAa,EAC1E,CAAC;QACH,CAAC,MAAM;UACL,IAAAE,yBAAa,EAAC,oBAAoB,EAAE,oDAAoD,CAAC;QAC3F;MACF;MACA,IAAI;QACF;QACA,OAAO,MAAMjC,YAAK,CAACkC,gBAAgB,CAACP,MAAM,CAAC7B,UAAU,CAACC,WAAW,CAAC;MACpE,CAAC,CAAC,OAAOkB,KAAU,EAAE;QACnB,IAAIU,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;UAChC;UACA,OAAO,EAAE;QACX;QACA,MAAMG,KAAK;MACb;IACF,CAAC;IACD,MAAMvB,IAAIA,CAACQ,QAAQ,EAAEyB,MAAM,EAAE;MAC3B;MACA;MACA,IAAI,CAACA,MAAM,CAACQ,GAAG,EAAER,MAAM,CAACQ,GAAG,GAAG,CAAC,CAAC;MAChC,IAAI,CAACR,MAAM,CAACQ,GAAG,CAACT,SAAS,EAAEC,MAAM,CAACQ,GAAG,CAACT,SAAS,GAAG,CAAC,CAAC;MAEpD,IAAItB,UAAqB;MACzB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMtC,QAAQ,CAACmC,QAAQ,EAAE,MAAM,CAAC;QACjD,IAAAkC,iBAAM,EAAC/B,QAAQ,EAAE,qBAAqB,CAAC;QACvCD,UAAU,GAAGW,gBAAK,CAACC,KAAK,CAACX,QAAQ,CAAc;MACjD,CAAC,CAAC,OAAOY,KAAU,EAAE;QACnB;QACA,IAAI,CAACU,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;UACjC,MAAMG,KAAK;QACb;QACA;QACAb,UAAU,GAAGjC,oBAAoB,CAAC,CAAC;MACrC;MAEAwD,MAAM,CAACQ,GAAG,CAACT,SAAS,GAAG;QACrB,IAAItB,UAAU,IAAI,CAAC,CAAC,CAAC;QACrB,GAAGuB,MAAM,CAACQ,GAAG,CAACT;MAChB,CAAC;MAED,OAAOC,MAAM,CAACQ,GAAG,CAACT,SAAS;IAC7B,CAAC;IACD,MAAM/B,KAAKA,CAACO,QAAQ,EAAEyB,MAAM,EAAE;MAC5B;MACA,IAAI,CAACA,MAAM,CAACQ,GAAG,EAAE;QACfR,MAAM,CAACQ,GAAG,GAAG,CAAC,CAAC;MACjB;MACAR,MAAM,CAACQ,GAAG,CAACT,SAAS,GAAGC,MAAM,CAACvB,UAAU;;MAExC;MACA,IAAIuB,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;QAChC;MACF;MAEA,MAAM9C,SAAS,CAACkC,QAAQ,EAAEa,gBAAK,CAACG,KAAK,CAAC,IAAAC,wBAAU,EAACQ,MAAM,CAACvB,UAAU,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,CAAC;EACF;EACAiC,YAAY,EAAE,IAAA7C,yBAAQ,EAAsC;IAC1De,eAAe,EAAE,IAAI;IAErB,MAAMd,WAAWA,CAACkC,MAAM,EAAE;MACxB,IAAI;QACF,IAAAW,iEAAiD,EAACX,MAAM,CAAC7B,UAAU,CAACC,WAAW,CAAC;QAChF,OAAOwC,mBAAY,CAACC,mBAAmB,CAACb,MAAM,CAAC7B,UAAU,CAACC,WAAW,CAAC,IAAI,EAAE;MAC9E,CAAC,CAAC,OAAOkB,KAAU,EAAE;QACnB,IAAIU,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;UAChC;UACA,OAAO,EAAE;QACX;QACA,MAAMG,KAAK;MACb;IACF,CAAC;IAED,MAAMvB,IAAIA,CAACQ,QAAQ,EAAEyB,MAAM,EAAE;MAC3B,IAAIvB,UAAsB;MAC1B,IAAI;QACF,IAAI,CAACuB,MAAM,CAAC7B,UAAU,CAAC2C,yBAAyB,IAAIC,aAAE,CAACC,UAAU,CAACzC,QAAQ,CAAC,EAAE;UAC3E,MAAMG,QAAQ,GAAG,MAAMtC,QAAQ,CAACmC,QAAQ,EAAE,MAAM,CAAC;UACjD,IAAAkC,iBAAM,EAAC/B,QAAQ,EAAE,6BAA6B,CAAC;UAC/CD,UAAU,GAAGW,gBAAK,CAACC,KAAK,CAACX,QAAQ,CAAC;QACpC,CAAC,MAAM;UACLD,UAAU,GAAGlC,4BAA4B,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC,OAAO+C,KAAU,EAAE;QACnB;QACA,IAAI,CAACU,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;UACjC,MAAMG,KAAK;QACb;QACA;QACAb,UAAU,GAAGlC,4BAA4B,CAAC,CAAC;MAC7C;;MAEA;MACA;MACA,IAAI,CAACyD,MAAM,CAACQ,GAAG,EAAER,MAAM,CAACQ,GAAG,GAAG,CAAC,CAAC;MAChC,IAAI,CAACR,MAAM,CAACQ,GAAG,CAACE,YAAY,EAAEV,MAAM,CAACQ,GAAG,CAACE,YAAY,GAAG,CAAC,CAAC;MAE1DV,MAAM,CAACQ,GAAG,CAACE,YAAY,GAAG;QACxB,IAAIjC,UAAU,IAAI,CAAC,CAAC,CAAC;QACrB,GAAGuB,MAAM,CAACQ,GAAG,CAACE;MAChB,CAAC;MAED,OAAOV,MAAM,CAACQ,GAAG,CAACE,YAAY;IAChC,CAAC;IAED,MAAM1C,KAAKA,CAACO,QAAQ,EAAEyB,MAAM,EAAE;MAC5B;MACA,IAAI,CAACA,MAAM,CAACQ,GAAG,EAAE;QACfR,MAAM,CAACQ,GAAG,GAAG,CAAC,CAAC;MACjB;MACAR,MAAM,CAACQ,GAAG,CAACE,YAAY,GAAGV,MAAM,CAACvB,UAAU;;MAE3C;MACA,IAAIuB,MAAM,CAAC7B,UAAU,CAACgB,UAAU,EAAE;QAChC;MACF;MAEA,MAAM9C,SAAS,CAACkC,QAAQ,EAAEa,gBAAK,CAACG,KAAK,CAAC,IAAAC,wBAAU,EAACQ,MAAM,CAACvB,UAAU,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,CAAC;EAEFwC,OAAO,EAAE,IAAApD,yBAAQ,EAA2B;IAC1CC,WAAWA,CAAC;MAAEK,UAAU,EAAE;QAAEC;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOC,YAAK,CAAC6C,cAAc,CAAC9C,WAAW,CAAC;IAC1C,CAAC;IACD;IACA,MAAML,IAAIA,CAACQ,QAAQ,EAAE;MACnB;MACA,OAAOF,YAAK,CAACG,WAAW,CAACD,QAAQ,CAAC;IACpC,CAAC;IACD,MAAMP,KAAKA,CAACO,QAAQ,EAAE;MAAEE,UAAU,EAAE;QAAEC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMrC,SAAS,CAACkC,QAAQ,EAAEG,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEF;EACAyC,iBAAiB,EAAE,IAAAtD,yBAAQ,EAA4B;IACrDe,eAAe,EAAE,IAAI;IAErBd,WAAWA,CAAC;MAAEK,UAAU,EAAE;QAAEU;MAAoB;IAAE,CAAC,EAAE;MACnD,OAAOG,eAAI,CAACE,OAAO,CAACL,mBAAmB,EAAE,yBAAyB,CAAC;IACrE,CAAC;IACD,MAAMd,IAAIA,CAACQ,QAAQ,EAAE;MACnB,IAAI6C,OAAkC,GAAG,CAAC,CAAC;MAC3C,IAAI;QACFA,OAAO,GAAG,MAAMC,mBAAQ,CAACC,SAAS,CAAC/C,QAAQ,CAAC;MAC9C,CAAC,CAAC,MAAM,CAAC;MACT,OAAO6C,OAAO;IAChB,CAAC;IACD,MAAMpD,KAAKA,CAACO,QAAQ,EAAE;MAAEE,UAAU;MAAEN,UAAU,EAAE;QAAEgB;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;QACd;MACF;MACA,MAAMkC,mBAAQ,CAACE,UAAU,CAAChD,QAAQ,EAAEE,UAAU,CAAC;IACjD;EACF,CAAC;AACH,CAAC;AAIM,SAAS+C,eAAeA,CAC7BxB,MAAsB,EACtB;EACEyB,SAAS;EACT,GAAGC;AACmE,CAAC,GAAG,CAAC,CAAC,EAC9D;EAChB,OAAO,IAAAC,sCAAqB,EAAa3B,MAAM,EAAE;IAC/C,GAAG0B,KAAK;IACRE,QAAQ,EAAE,KAAK;IACfH,SAAS,EAAEA,SAAS,IAAII,sBAAsB,CAAC;EACjD,CAAC,CAAC;AACJ;AAEO,SAASA,sBAAsBA,CAAA,EAAG;EACvC,OAAOlE,gBAAgB;AACzB", "ignoreList": []}