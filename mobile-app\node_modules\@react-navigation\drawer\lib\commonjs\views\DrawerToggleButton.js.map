{"version": 3, "names": ["Drawer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tintColor", "rest", "navigation", "useNavigation", "borderless", "dispatch", "DrawerActions", "toggle<PERSON>rawer", "styles", "touchable", "Platform", "select", "ios", "undefined", "default", "top", "right", "bottom", "left", "icon", "require", "StyleSheet", "create", "height", "width", "margin", "resizeMode", "marginHorizontal"], "sourceRoot": "../../../src", "sources": ["views/DrawerToggleButton.tsx"], "mappings": ";;;;;;AAAA;AACA;AAKA;AACA;AAA2D;AAAA;AAAA;AAW5C,SAASA,kBAAkB,OAAgC;EAAA,IAA/B;IAAEC,SAAS;IAAE,GAAGC;EAAY,CAAC;EACtE,MAAMC,UAAU,GAAG,IAAAC,qBAAa,GAAuC;EAEvE,oBACE,oBAAC,2BAAiB,eACZF,IAAI;IACR,UAAU;IACV,iBAAiB,EAAC,QAAQ;IAC1B,cAAc,EAAE;MAAEG,UAAU,EAAE;IAAK,CAAE;IACrC,OAAO,EAAE,MAAMF,UAAU,CAACG,QAAQ,CAACC,qBAAa,CAACC,YAAY,EAAE,CAAE;IACjE,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxB,OAAO,EAAEC,qBAAQ,CAACC,MAAM,CAAC;MACvBC,GAAG,EAAEC,SAAS;MACdC,OAAO,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG;IACtD,CAAC;EAAE,iBAEH,oBAAC,kBAAK;IACJ,KAAK,EAAE,CAACV,MAAM,CAACW,IAAI,EAAEnB,SAAS,GAAG;MAAEA;IAAU,CAAC,GAAG,IAAI,CAAE;IACvD,MAAM,EAAEoB,OAAO,CAAC,iCAAiC,CAAE;IACnD,YAAY,EAAE;EAAE,EAChB,CACgB;AAExB;AAEA,MAAMZ,MAAM,GAAGa,uBAAU,CAACC,MAAM,CAAC;EAC/BH,IAAI,EAAE;IACJI,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd,CAAC;EACDjB,SAAS,EAAE;IACTkB,gBAAgB,EAAE;EACpB;AACF,CAAC,CAAC"}