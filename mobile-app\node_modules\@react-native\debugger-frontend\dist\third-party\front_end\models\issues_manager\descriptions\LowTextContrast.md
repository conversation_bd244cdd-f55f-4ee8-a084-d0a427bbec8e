# Users may have difficulties reading text content due to insufficient color contrast

Low-contrast text is difficult or impossible for users to read. A [minimum contrast ratio (AA) of 4.5](issuesContrastWCAG21AA) is recommended for all text. Since font size and weight affect color perception, an exception is made for very large or bold text — in this case, a contrast ratio of 3.0 is allowed. The [enhanced conformance level (AAA)](issuesContrastWCAG21AAA) requires the contrast ratio to be above 7.0 for regular text and 4.5 for large text.

Update colors or change the font size or weight to achieve sufficient contrast. You can use the [“Suggest color” feature](issuesContrastSuggestColor) in the DevTools color picker to automatically select a better text color.